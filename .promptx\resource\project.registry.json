{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-30T21:59:40.536Z", "updatedAt": "2025-07-30T21:59:40.551Z", "resourceCount": 25}, "resources": [{"id": "interview-process", "source": "project", "protocol": "execution", "name": "Interview Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/execution/interview-process.execution.md", "metadata": {"createdAt": "2025-07-30T21:59:40.539Z", "updatedAt": "2025-07-30T21:59:40.539Z", "scannedAt": "2025-07-30T21:59:40.539Z", "path": "domain/java-ai-interviewer/execution/interview-process.execution.md"}}, {"id": "question-generation", "source": "project", "protocol": "execution", "name": "Question Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/execution/question-generation.execution.md", "metadata": {"createdAt": "2025-07-30T21:59:40.539Z", "updatedAt": "2025-07-30T21:59:40.539Z", "scannedAt": "2025-07-30T21:59:40.539Z", "path": "domain/java-ai-interviewer/execution/question-generation.execution.md"}}, {"id": "resume-analysis", "source": "project", "protocol": "execution", "name": "Resume Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/execution/resume-analysis.execution.md", "metadata": {"createdAt": "2025-07-30T21:59:40.540Z", "updatedAt": "2025-07-30T21:59:40.540Z", "scannedAt": "2025-07-30T21:59:40.540Z", "path": "domain/java-ai-interviewer/execution/resume-analysis.execution.md"}}, {"id": "java-ai-interviewer", "source": "project", "protocol": "role", "name": "Java Ai Interviewer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/java-ai-interviewer.role.md", "metadata": {"createdAt": "2025-07-30T21:59:40.540Z", "updatedAt": "2025-07-30T21:59:40.540Z", "scannedAt": "2025-07-30T21:59:40.540Z", "path": "domain/java-ai-interviewer/java-ai-interviewer.role.md"}}, {"id": "hr-recruitment-standards", "source": "project", "protocol": "knowledge", "name": "Hr Recruitment Standards 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/knowledge/hr-recruitment-standards.knowledge.md", "metadata": {"createdAt": "2025-07-30T21:59:40.541Z", "updatedAt": "2025-07-30T21:59:40.541Z", "scannedAt": "2025-07-30T21:59:40.541Z", "path": "domain/java-ai-interviewer/knowledge/hr-recruitment-standards.knowledge.md"}}, {"id": "interview-methodology", "source": "project", "protocol": "knowledge", "name": "Interview Methodology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/knowledge/interview-methodology.knowledge.md", "metadata": {"createdAt": "2025-07-30T21:59:40.541Z", "updatedAt": "2025-07-30T21:59:40.541Z", "scannedAt": "2025-07-30T21:59:40.541Z", "path": "domain/java-ai-interviewer/knowledge/interview-methodology.knowledge.md"}}, {"id": "java-ai-technical-stack", "source": "project", "protocol": "knowledge", "name": "Java Ai Technical Stack 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/knowledge/java-ai-technical-stack.knowledge.md", "metadata": {"createdAt": "2025-07-30T21:59:40.541Z", "updatedAt": "2025-07-30T21:59:40.541Z", "scannedAt": "2025-07-30T21:59:40.541Z", "path": "domain/java-ai-interviewer/knowledge/java-ai-technical-stack.knowledge.md"}}, {"id": "behavioral-assessment", "source": "project", "protocol": "thought", "name": "Behavioral Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/thought/behavioral-assessment.thought.md", "metadata": {"createdAt": "2025-07-30T21:59:40.542Z", "updatedAt": "2025-07-30T21:59:40.542Z", "scannedAt": "2025-07-30T21:59:40.542Z", "path": "domain/java-ai-interviewer/thought/behavioral-assessment.thought.md"}}, {"id": "multi-perspective-evaluation", "source": "project", "protocol": "thought", "name": "Multi Perspective Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/thought/multi-perspective-evaluation.thought.md", "metadata": {"createdAt": "2025-07-30T21:59:40.542Z", "updatedAt": "2025-07-30T21:59:40.542Z", "scannedAt": "2025-07-30T21:59:40.542Z", "path": "domain/java-ai-interviewer/thought/multi-perspective-evaluation.thought.md"}}, {"id": "skeptical-validation", "source": "project", "protocol": "thought", "name": "Skeptical Validation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/thought/skeptical-validation.thought.md", "metadata": {"createdAt": "2025-07-30T21:59:40.543Z", "updatedAt": "2025-07-30T21:59:40.543Z", "scannedAt": "2025-07-30T21:59:40.543Z", "path": "domain/java-ai-interviewer/thought/skeptical-validation.thought.md"}}, {"id": "technical-depth-assessment", "source": "project", "protocol": "thought", "name": "Technical Depth Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/thought/technical-depth-assessment.thought.md", "metadata": {"createdAt": "2025-07-30T21:59:40.543Z", "updatedAt": "2025-07-30T21:59:40.543Z", "scannedAt": "2025-07-30T21:59:40.543Z", "path": "domain/java-ai-interviewer/thought/technical-depth-assessment.thought.md"}}, {"id": "interview-recording", "source": "project", "protocol": "execution", "name": "Interview Recording 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/execution/interview-recording.execution.md", "metadata": {"createdAt": "2025-07-30T21:59:40.544Z", "updatedAt": "2025-07-30T21:59:40.544Z", "scannedAt": "2025-07-30T21:59:40.544Z", "path": "domain/pdr-interview-coordinator/execution/interview-recording.execution.md"}}, {"id": "mock-interview-process", "source": "project", "protocol": "execution", "name": "Mock Interview Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/execution/mock-interview-process.execution.md", "metadata": {"createdAt": "2025-07-30T21:59:40.544Z", "updatedAt": "2025-07-30T21:59:40.544Z", "scannedAt": "2025-07-30T21:59:40.544Z", "path": "domain/pdr-interview-coordinator/execution/mock-interview-process.execution.md"}}, {"id": "role-activation-management", "source": "project", "protocol": "execution", "name": "Role Activation Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/execution/role-activation-management.execution.md", "metadata": {"createdAt": "2025-07-30T21:59:40.545Z", "updatedAt": "2025-07-30T21:59:40.545Z", "scannedAt": "2025-07-30T21:59:40.545Z", "path": "domain/pdr-interview-coordinator/execution/role-activation-management.execution.md"}}, {"id": "interview-simulation-methodology", "source": "project", "protocol": "knowledge", "name": "Interview Simulation Methodology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/knowledge/interview-simulation-methodology.knowledge.md", "metadata": {"createdAt": "2025-07-30T21:59:40.545Z", "updatedAt": "2025-07-30T21:59:40.545Z", "scannedAt": "2025-07-30T21:59:40.545Z", "path": "domain/pdr-interview-coordinator/knowledge/interview-simulation-methodology.knowledge.md"}}, {"id": "pdr-process-framework", "source": "project", "protocol": "knowledge", "name": "Pdr Process Framework 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/knowledge/pdr-process-framework.knowledge.md", "metadata": {"createdAt": "2025-07-30T21:59:40.546Z", "updatedAt": "2025-07-30T21:59:40.546Z", "scannedAt": "2025-07-30T21:59:40.546Z", "path": "domain/pdr-interview-coordinator/knowledge/pdr-process-framework.knowledge.md"}}, {"id": "role-coordination-standards", "source": "project", "protocol": "knowledge", "name": "Role Coordination Standards 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/knowledge/role-coordination-standards.knowledge.md", "metadata": {"createdAt": "2025-07-30T21:59:40.546Z", "updatedAt": "2025-07-30T21:59:40.546Z", "scannedAt": "2025-07-30T21:59:40.546Z", "path": "domain/pdr-interview-coordinator/knowledge/role-coordination-standards.knowledge.md"}}, {"id": "pdr-interview-coordinator", "source": "project", "protocol": "role", "name": "Pdr Interview Coordinator 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/pdr-interview-coordinator.role.md", "metadata": {"createdAt": "2025-07-30T21:59:40.547Z", "updatedAt": "2025-07-30T21:59:40.547Z", "scannedAt": "2025-07-30T21:59:40.547Z", "path": "domain/pdr-interview-coordinator/pdr-interview-coordinator.role.md"}}, {"id": "interview-orchestration", "source": "project", "protocol": "thought", "name": "Interview Orchestration 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/thought/interview-orchestration.thought.md", "metadata": {"createdAt": "2025-07-30T21:59:40.548Z", "updatedAt": "2025-07-30T21:59:40.548Z", "scannedAt": "2025-07-30T21:59:40.548Z", "path": "domain/pdr-interview-coordinator/thought/interview-orchestration.thought.md"}}, {"id": "process-management", "source": "project", "protocol": "thought", "name": "Process Management 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/thought/process-management.thought.md", "metadata": {"createdAt": "2025-07-30T21:59:40.548Z", "updatedAt": "2025-07-30T21:59:40.548Z", "scannedAt": "2025-07-30T21:59:40.548Z", "path": "domain/pdr-interview-coordinator/thought/process-management.thought.md"}}, {"id": "role-coordination", "source": "project", "protocol": "thought", "name": "Role Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/pdr-interview-coordinator/thought/role-coordination.thought.md", "metadata": {"createdAt": "2025-07-30T21:59:40.548Z", "updatedAt": "2025-07-30T21:59:40.548Z", "scannedAt": "2025-07-30T21:59:40.548Z", "path": "domain/pdr-interview-coordinator/thought/role-coordination.thought.md"}}, {"id": "resume-crafting", "source": "project", "protocol": "execution", "name": "Resume Crafting 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/resume-master/execution/resume-crafting.execution.md", "metadata": {"createdAt": "2025-07-30T21:59:40.549Z", "updatedAt": "2025-07-30T21:59:40.549Z", "scannedAt": "2025-07-30T21:59:40.549Z", "path": "domain/resume-master/execution/resume-crafting.execution.md"}}, {"id": "resume-expertise", "source": "project", "protocol": "knowledge", "name": "Resume Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/resume-master/knowledge/resume-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-30T21:59:40.550Z", "updatedAt": "2025-07-30T21:59:40.550Z", "scannedAt": "2025-07-30T21:59:40.550Z", "path": "domain/resume-master/knowledge/resume-expertise.knowledge.md"}}, {"id": "resume-master", "source": "project", "protocol": "role", "name": "Resume Master 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/resume-master/resume-master.role.md", "metadata": {"createdAt": "2025-07-30T21:59:40.550Z", "updatedAt": "2025-07-30T21:59:40.550Z", "scannedAt": "2025-07-30T21:59:40.550Z", "path": "domain/resume-master/resume-master.role.md"}}, {"id": "resume-psychology", "source": "project", "protocol": "thought", "name": "Resume Psychology 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/resume-master/thought/resume-psychology.thought.md", "metadata": {"createdAt": "2025-07-30T21:59:40.550Z", "updatedAt": "2025-07-30T21:59:40.550Z", "scannedAt": "2025-07-30T21:59:40.550Z", "path": "domain/resume-master/thought/resume-psychology.thought.md"}}], "stats": {"totalResources": 25, "byProtocol": {"execution": 7, "role": 3, "knowledge": 7, "thought": 8}, "bySource": {"project": 25}}}