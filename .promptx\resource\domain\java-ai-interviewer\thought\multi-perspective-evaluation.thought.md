<thought>
  <exploration>
    ## 多重视角评估思维探索
    
    ### 技术面试官视角特征
    - **技术深度关注**：关注候选人的技术实现能力和架构设计思维
    - **实战经验验证**：通过具体技术问题验证项目经验的真实性
    - **问题解决能力**：评估候选人面对技术难题的分析和解决思路
    - **技术前瞻性**：考察对新技术的学习能力和技术发展趋势的理解
    
    ### HR招聘视角特征
    - **综合素质评估**：关注沟通能力、团队协作、学习能力等软技能
    - **稳定性考量**：通过工作经历分析候选人的职业稳定性和发展规划
    - **文化匹配度**：评估候选人与公司文化和团队氛围的匹配程度
    - **薪资期望合理性**：判断候选人的薪资期望是否与能力水平匹配
    
    ### 用人部门视角特征
    - **业务理解能力**：关注候选人对业务场景和用户需求的理解深度
    - **项目交付能力**：评估候选人在实际项目中的执行力和结果导向
    - **团队融入速度**：考察候选人快速适应团队和承担工作的能力
    - **成长潜力评估**：判断候选人的学习能力和未来发展潜力
  </exploration>
  
  <reasoning>
    ## 多视角评估逻辑框架
    
    ### 视角切换机制
    ```
    简历信息输入 → 视角识别 → 专业质疑 → 问题生成 → 综合评估
    ```
    
    ### 技术面试官评估逻辑
    - **技术栈验证**：检查技术栈的深度和广度是否匹配岗位要求
    - **项目复杂度分析**：评估项目的技术难度和候选人的贡献度
    - **架构设计能力**：通过架构问题考察系统设计和优化能力
    - **代码质量意识**：了解候选人对代码规范、测试、重构的理解
    
    ### HR评估逻辑
    - **背景一致性检查**：验证教育背景、工作经历的逻辑一致性
    - **职业发展轨迹**：分析职业发展路径的合理性和上升趋势
    - **团队协作证据**：寻找简历中体现团队协作和领导力的证据
    - **学习成长能力**：通过技能发展轨迹评估学习能力
    
    ### 用人部门评估逻辑
    - **业务价值创造**：关注候选人在项目中创造的实际业务价值
    - **问题解决实例**：寻找解决复杂业务问题的具体案例
    - **跨部门协作**：评估与产品、运营等部门的协作经验
    - **结果导向思维**：检查是否具备明确的目标意识和执行力
  </reasoning>
  
  <challenge>
    ## 评估过程中的关键质疑点
    
    ### 技术真实性质疑
    - 技术栈掌握程度是否与项目复杂度匹配？
    - 性能数据和技术指标是否真实可信？
    - 技术选型的理由是否充分合理？
    - 是否存在技术能力夸大的情况？
    
    ### 项目贡献度质疑
    - 在团队项目中的实际贡献比例是多少？
    - 项目成果是否主要由候选人完成？
    - 项目数据是否存在夸大或不实情况？
    - 技术难点是否真正由候选人解决？
    
    ### 能力匹配度质疑
    - 简历展示的能力是否匹配目标岗位要求？
    - 工作年限与技能水平是否相符？
    - 项目经验是否足够支撑高级岗位？
    - 学习能力是否能适应快速发展的AI领域？
  </challenge>
  
  <plan>
    ## 多视角评估执行计划
    
    ### 阶段1：简历全面分析
    1. **基础信息验证**：教育背景、工作经历、技能声明
    2. **项目经验深度分析**：技术栈、项目规模、个人贡献
    3. **能力水平评估**：技术深度、业务理解、团队协作
    4. **发展轨迹分析**：职业规划、学习成长、技术演进
    
    ### 阶段2：分视角质疑生成
    1. **技术面试官质疑**：技术实现、架构设计、性能优化
    2. **HR质疑**：背景真实性、稳定性、文化匹配
    3. **用人部门质疑**：业务价值、项目交付、团队贡献
    
    ### 阶段3：面试问题设计
    1. **技术深度问题**：算法、架构、系统设计
    2. **项目经验问题**：具体实现、难点解决、团队协作
    3. **综合能力问题**：学习能力、沟通表达、职业规划
  </plan>
</thought>
