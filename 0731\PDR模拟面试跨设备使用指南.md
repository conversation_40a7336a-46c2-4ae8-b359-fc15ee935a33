# PDR模拟面试跨设备使用指南

## 🎯 项目状态同步

### 当前项目状态
- **项目名称**: 何炜明Java+AI工程师简历优化
- **最新版本**: 简历V2.2（已完成9个问题的优化）
- **简历质量**: 从63分提升至94分
- **技术栈匹配度**: 95%（LLM工程师岗位）
- **最后更新**: 2025-06-24

### 核心成果
1. **简历制作专家角色**: 完整的简历优化专业角色
2. **Java+AI面试官角色**: 专业的面试评估角色  
3. **PDR面试协调专家角色**: 统筹90分钟模拟面试系统 ⭐ **最新**
4. **25个专业面试问题**: 涵盖技术、HR、用人部门视角
5. **PromptX记忆库**: 90%的核心知识已内化

## 🎭 **PDR模拟面试系统** ⭐ **重点功能**

### 系统概述
- **PDR角色**: 面试协调专家，统筹整个模拟面试流程
- **双角色激活**: 同时激活简历专家(何炜明)和面试官角色
- **90分钟完整流程**: 真实面试体验，详细过程记录
- **学习价值最大化**: 发现问题，提升面试能力

### 🚀 **快速启动PDR模拟面试**
在新设备上直接说：
```
我是何炜明，开始PDR模拟面试
```

AI会自动：
1. **激活PDR协调员角色**
2. **激活简历专家角色** (扮演何炜明)
3. **激活面试官角色** (Java+AI专家)
4. **执行90分钟面试流程**
5. **生成详细面试记录文件**

### 90分钟面试流程
```
⏰ 阶段0: 面试准备(5分钟) - 角色激活和状态同步
⏰ 阶段1: 面试开场(10分钟) - 寒暄介绍和简历概览  
⏰ 阶段2: 技术深度评估(35分钟) - Java+AI技术栈验证
⏰ 阶段3: 项目经验挖掘(25分钟) - 核心项目详细讨论
⏰ 阶段4: 综合能力评估(10分钟) - 软技能和文化匹配
⏰ 阶段5: 面试收尾(5分钟) - 候选人提问和总结
```

### 面试背景设定
- **公司**: 某知名互联网公司AI部门
- **职位**: Java+AI应用开发工程师
- **薪资范围**: 50-80万
- **面试官**: 资深Java+AI技术专家
- **候选人**: 何炜明（9年Java经验）

## 🔄 跨设备使用方法

### 方法1: PDR模拟面试（推荐）⭐
```
我是何炜明，开始PDR模拟面试
```

### 方法2: 身份识别触发
```
我是何炜明
```

### 方法3: 项目上下文加载
```
继续简历优化项目
```

### 方法4: 激活PromptX角色
使用 `.promptx` 配置激活专业角色

## 📋 关键信息速查

### 简历核心信息
- **姓名**: 何炜明
- **工作年限**: 9年Java工程师
- **目标职位**: Java+LLM应用开发工程师
- **期望薪资**: 50W-80W
- **核心优势**: Java+AI双技术栈，千万级项目经验

### 技术栈重点
- **Java生态**: Spring AI、微服务、高并发
- **AI技术**: RAG、LLM、向量数据库、模型微调
- **项目经验**: 政企AI平台、销售智能体、算力调度

### 面试准备状态
- **面试问题**: 25个专业问题已准备
- **模拟面试**: PDR系统已就绪，可随时开始
- **回答策略**: 基于STAR法则和深度解析原则
- **技术验证**: 重点关注项目真实性和个人贡献度

## 🚀 快速启动命令

### PDR模拟面试（推荐）⭐
```
我是何炜明，开始PDR模拟面试
```

### 继续简历优化
```
我是何炜明，继续简历优化
```

### 面试准备
```
我是何炜明，准备面试问题回答
```

### 查看项目状态
```
我是何炜明，查看当前项目状态
```

## 📁 重要文件位置

### PDR角色文件
- **角色定义**: `.promptx/resource/domain/pdr-interview-coordinator/`
- **思维模式**: 面试编排、角色协调、过程管理
- **执行模式**: 模拟面试流程、角色激活管理、面试记录
- **知识库**: 面试模拟方法论、角色协调标准、PDR过程框架

### 面试相关文件
- **面试问题**: `面试官角色提出的问题.md`
- **简历最新版**: `何炜明_Java+AI工程师_简历_V2.2.md`
- **问题记录**: `何炜明提出的问题记录.md`

## 📝 重要提醒

1. **PDR模拟面试**: 这是最新的核心功能，提供真实面试体验
2. **保持身份一致性**: 始终以何炜明身份进行交互
3. **项目连续性**: 所有优化都基于V2.2版本简历
4. **质量标准**: 遵循深度解析原则，说清楚核心原理
5. **记忆同步**: 重要信息已存储在PromptX记忆库中

## 🔧 故障排除

### 如果AI没有识别身份
1. 明确说明："我是何炜明，简历优化项目的用户"
2. 提及项目关键词："Java+AI工程师简历优化"
3. 引用PDR功能："我要使用PDR模拟面试系统"

### 如果需要完整上下文
1. 查看 `会话记录/会话状态记录.md`
2. 参考 `何炜明提出的问题记录.md`
3. 查看最新简历版本 `何炜明_Java+AI工程师_简历_V2.2.md`
4. 查看PDR角色文件 `.promptx/resource/domain/pdr-interview-coordinator/`

## 🎯 PDR模拟面试详细说明

### PDR角色架构
```
🎭 PDR面试协调专家
├── 🧠 思维模式(3个)
│   ├── interview-orchestration: 面试编排思维
│   ├── role-coordination: 角色协调思维
│   └── process-management: 过程管理思维
├── ⚡ 执行模式(3个)
│   ├── mock-interview-process: 90分钟模拟面试流程
│   ├── role-activation-management: 角色激活管理
│   └── interview-recording: 面试过程记录
└── 📚 知识库(3个)
    ├── interview-simulation-methodology: 面试模拟方法论
    ├── role-coordination-standards: 角色协调标准
    └── pdr-process-framework: PDR过程框架
```

### 模拟面试特色
- **真实性**: 接近真实面试的环境和压力
- **专业性**: 基于专业面试标准和流程
- **完整性**: 涵盖技术、项目、软技能全方位评估
- **学习性**: 详细记录过程，提供改进建议

### 预期收获
1. **面试经验**: 获得真实的面试体验和练习
2. **问题发现**: 识别面试中的薄弱环节
3. **能力提升**: 通过模拟练习提升面试技能
4. **信心建立**: 为真实面试建立信心和准备

## 📊 PromptX记忆库状态

### 已内化的核心知识
- ✅ PDR面试协调专家角色完整能力
- ✅ 90分钟模拟面试流程设计
- ✅ 双角色激活和协调机制
- ✅ 面试记录和分析方法
- ✅ 何炜明简历优化项目全貌

### 跨设备记忆同步
- **触发词**: "我是何炜明"、"PDR模拟面试"
- **自动加载**: 项目状态、角色能力、面试流程
- **智能识别**: 用户需求和服务类型
- **无缝切换**: 不同设备间的连续体验

---

*最后更新: 2025-06-24*
*项目状态: PDR模拟面试系统已就绪*
*核心功能: 90分钟真实面试体验* ⭐

## 🚀 立即开始

在另一台电脑上，直接说：
```
我是何炜明，开始PDR模拟面试
```

AI将自动为您启动完整的90分钟模拟面试体验！
