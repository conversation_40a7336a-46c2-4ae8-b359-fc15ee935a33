<execution>
  <constraint>
    ## 面试记录客观限制
    - **实时性要求**：需要在面试进行中实时记录
    - **完整性需求**：需要记录90分钟的完整面试过程
    - **结构化要求**：记录需要有清晰的结构和格式
    - **可读性要求**：记录需要便于后续分析和学习
  </constraint>

  <rule>
    ## 面试记录强制规则
    - **实时记录**：必须在面试过程中实时记录关键信息
    - **完整覆盖**：必须覆盖面试的每个重要阶段和环节
    - **格式统一**：必须使用统一的记录格式和标准
    - **客观准确**：记录内容必须客观准确，避免主观臆断
  </rule>

  <guideline>
    ## 面试记录指导原则
    - **重点突出**：突出记录关键问题、回答和评估
    - **层次清晰**：按照面试阶段和时间顺序组织记录
    - **细节适度**：既要有必要细节，又要避免冗余
    - **分析导向**：记录要便于后续分析和改进
  </guideline>

  <process>
    ## 面试记录完整流程

    ### 记录模板结构
    ```
    📋 90分钟模拟面试完整记录
    
    ═══════════════════════════════════════
    📊 面试基本信息
    ═══════════════════════════════════════
    🕐 面试时间：[开始时间] - [结束时间]
    ⏱️ 面试时长：90分钟
    🎭 参与角色：PDR协调员、Java+AI面试官、何炜明
    🏢 面试背景：某知名互联网公司 - Java+AI应用开发工程师
    💰 薪资范围：50-80万
    🎯 面试目标：全面评估技术能力、项目经验、综合素质
    
    ═══════════════════════════════════════
    📝 面试过程详细记录
    ═══════════════════════════════════════
    ```

    ### 阶段1: 面试准备记录 (5分钟)
    ```
    ⏰ 【00:00-00:05】面试准备阶段
    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    
    🔄 [00:00] PDR协调员启动
    📋 设定面试背景和参数
    - 公司：某知名互联网公司AI部门
    - 职位：Java+AI应用开发工程师
    - 面试官：资深技术专家
    - 候选人：何炜明（9年Java经验）
    
    🎭 [00:02] 角色激活过程
    ✅ 简历专家角色激活 → 何炜明身份
    ✅ 面试官角色激活 → Java+AI面试专家
    ✅ 状态同步确认完成
    
    📊 准备阶段质量评估：[优秀/良好/一般]
    📝 备注：[特殊情况或调整说明]
    ```

    ### 阶段2: 面试开场记录 (10分钟)
    ```
    ⏰ 【00:05-00:15】面试开场阶段
    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    
    🎭 [00:05] 面试官开场
    [面试官]: "您好何炜明，欢迎来到我们公司面试。我是技术部门的资深工程师，今天将负责您的技术面试。首先请您简单自我介绍一下。"
    
    🎭 [00:06] 何炜明自我介绍
    [何炜明]: [记录具体的自我介绍内容]
    
    📊 开场表现评估：
    - 面试官：专业性 [★★★★★] 友好度 [★★★★★]
    - 何炜明：自信度 [★★★★★] 表达清晰度 [★★★★★]
    
    🔍 [00:08] 简历概览讨论
    [面试官]: [记录面试官对简历的初步印象和疑问]
    [何炜明]: [记录何炜明的回应和补充说明]
    
    📝 重点关注：
    - 面试官标记的技术验证重点
    - 何炜明强调的核心优势
    - 双方建立的面试基调
    
    📊 开场阶段总结：
    - 氛围营造：[成功/良好/需改进]
    - 初步印象：[正面/中性/负面]
    - 后续重点：[确定的面试重点方向]
    ```

    ### 阶段3: 技术深度评估记录 (35分钟)
    ```
    ⏰ 【00:15-00:50】技术深度评估阶段
    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    
    🔧 [00:15-00:30] Java技术栈验证 (15分钟)
    ┌─────────────────────────────────────┐
    │ 核心问题1: 并发编程和JVM调优        │
    └─────────────────────────────────────┘
    [面试官]: "您提到支撑2000+ QPS的系统，具体的并发处理策略是什么？"
    [何炜明]: [记录详细回答内容]
    
    📊 回答质量评估：
    - 技术深度：[★★★★★]
    - 实践经验：[★★★★★]
    - 表达清晰度：[★★★★★]
    
    ┌─────────────────────────────────────┐
    │ 核心问题2: Spring生态和微服务架构   │
    └─────────────────────────────────────┘
    [面试官]: [记录具体问题]
    [何炜明]: [记录具体回答]
    
    🤖 [00:30-00:45] AI技术栈验证 (15分钟)
    ┌─────────────────────────────────────┐
    │ 核心问题1: RAG系统架构和优化        │
    └─────────────────────────────────────┘
    [面试官]: "RAG系统检索准确率从85%提升到92%，具体优化了什么？"
    [何炜明]: [记录详细回答内容]
    
    📊 技术验证结果：
    - Java技术栈掌握度：[专家/高级/中级/初级]
    - AI技术栈应用能力：[专家/高级/中级/初级]
    - 系统架构设计能力：[专家/高级/中级/初级]
    
    🏗️ [00:45-00:50] 系统设计问题 (5分钟)
    [面试官]: "请设计一个支撑千万用户的AI问答系统"
    [何炜明]: [记录设计思路和方案]
    
    📝 技术评估阶段总结：
    - 技术深度表现：[超出预期/符合预期/低于预期]
    - 实践经验验证：[真实可信/基本可信/存疑]
    - 创新思维展现：[突出/一般/不足]
    ```

    ### 阶段4: 项目经验挖掘记录 (25分钟)
    ```
    ⏰ 【00:50-01:15】项目经验深度挖掘阶段
    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    
    🏢 [00:50-00:58] 政企AI知识助手平台 (8分钟)
    ┌─────────────────────────────────────┐
    │ 项目背景和技术挑战                  │
    └─────────────────────────────────────┘
    [面试官]: "请详细介绍政企AI知识助手平台的技术架构"
    [何炜明]: [记录项目介绍内容]
    
    ┌─────────────────────────────────────┐
    │ 个人贡献和技术难点                  │
    └─────────────────────────────────────┘
    [面试官]: "在这个项目中，您个人的核心技术贡献是什么？"
    [何炜明]: [记录个人贡献说明]
    
    📊 项目经验评估：
    - 项目复杂度：[高/中/低]
    - 个人贡献度：[核心/重要/一般]
    - 技术创新性：[突出/一般/不足]
    
    💼 [00:58-01:05] 销售智能体SaaS平台 (7分钟)
    [面试官]: "8个月6000万交易额是如何实现的？"
    [何炜明]: [记录实现路径说明]
    
    🖥️ [01:05-01:10] 算力调度平台 (5分钟)
    [面试官]: "国家级项目的技术要求和挑战是什么？"
    [何炜明]: [记录项目挑战和解决方案]
    
    🔍 [01:10-01:15] 项目真实性验证 (5分钟)
    [面试官]: [记录验证性问题]
    [何炜明]: [记录回答和解释]
    
    📝 项目经验阶段总结：
    - 项目经验丰富度：[非常丰富/丰富/一般/不足]
    - 业务价值理解：[深入/良好/一般/不足]
    - 数据真实性：[可信/基本可信/存疑]
    ```

    ### 阶段5: 综合能力评估记录 (10分钟)
    ```
    ⏰ 【01:15-01:25】综合能力评估阶段
    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    
    🤝 [01:15-01:20] 软技能评估 (5分钟)
    ┌─────────────────────────────────────┐
    │ 沟通能力和团队协作                  │
    └─────────────────────────────────────┘
    [面试官]: "作为技术负责人，您是如何管理5人团队的？"
    [何炜明]: [记录团队管理经验]
    
    📊 软技能评估：
    - 沟通表达：[优秀/良好/一般/不足]
    - 团队协作：[优秀/良好/一般/不足]
    - 学习能力：[优秀/良好/一般/不足]
    - 抗压能力：[优秀/良好/一般/不足]
    
    🏢 [01:20-01:25] 文化匹配度评估 (5分钟)
    [面试官]: "您的职业规划是什么？为什么选择我们公司？"
    [何炜明]: [记录职业规划和选择原因]
    
    📊 文化匹配度：
    - 价值观匹配：[高度匹配/匹配/一般/不匹配]
    - 发展规划：[清晰合理/基本合理/模糊/不合理]
    - 稳定性预期：[高/中/低]
    ```

    ### 阶段6: 面试收尾记录 (5分钟)
    ```
    ⏰ 【01:25-01:30】面试收尾阶段
    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    
    ❓ [01:25-01:28] 候选人提问环节 (3分钟)
    [何炜明]: [记录何炜明提出的问题]
    [面试官]: [记录面试官的回答]
    
    📊 提问质量评估：
    - 问题专业性：[高/中/低]
    - 关注重点：[技术发展/团队文化/业务前景/薪资福利]
    - 求职诚意：[强烈/一般/不足]
    
    🎯 [01:28-01:30] 面试总结 (2分钟)
    [面试官]: [记录面试官的总结评价]
    - 整体印象：[正面/中性/负面]
    - 技术能力评估：[符合要求/基本符合/不符合]
    - 推荐等级：[强烈推荐/推荐/谨慎考虑/不推荐]
    ```

    ### 面试总结报告
    ```
    ═══════════════════════════════════════
    📊 90分钟模拟面试总结报告
    ═══════════════════════════════════════
    
    📈 整体表现评估：
    ┌─────────────────────────────────────┐
    │ 技术能力：[★★★★★] 8.5/10           │
    │ 项目经验：[★★★★☆] 8.0/10           │
    │ 沟通协作：[★★★★☆] 7.5/10           │
    │ 文化匹配：[★★★★☆] 8.0/10           │
    │ 综合评分：[★★★★☆] 8.0/10           │
    └─────────────────────────────────────┘
    
    ✅ 表现优秀的方面：
    1. [具体优势点1]
    2. [具体优势点2]
    3. [具体优势点3]
    
    ⚠️ 需要改进的方面：
    1. [具体问题点1及改进建议]
    2. [具体问题点2及改进建议]
    3. [具体问题点3及改进建议]
    
    🎯 面试官最终评估：
    - 推荐等级：[强烈推荐/推荐/谨慎考虑/不推荐]
    - 主要理由：[评估理由说明]
    - 薪资建议：[薪资范围建议]
    
    💡 对真实面试的建议：
    1. [准备建议1]
    2. [准备建议2]
    3. [准备建议3]
    
    📝 模拟面试质量评估：
    - 真实性：[★★★★★]
    - 专业性：[★★★★★]
    - 学习价值：[★★★★★]
    - 角色协调：[★★★★★]
    ```
  </process>

  <criteria>
    ## 面试记录质量标准

    ### 记录完整性标准
    - **时间覆盖**：完整覆盖90分钟面试过程
    - **内容完整**：记录所有重要问题、回答和评估
    - **结构清晰**：按照阶段和时间顺序组织记录
    - **细节适度**：既有必要细节又避免冗余

    ### 记录准确性标准
    - **客观真实**：准确记录实际发生的对话和互动
    - **评估公正**：客观记录角色表现和质量评估
    - **时间准确**：准确记录各阶段的时间节点
    - **格式统一**：使用统一的记录格式和标准

    ### 分析价值标准
    - **问题识别**：准确识别面试中的问题和不足
    - **改进建议**：提供具体可操作的改进建议
    - **学习指导**：为真实面试提供有价值的指导
    - **经验总结**：总结有价值的面试经验和教训
  </criteria>
</execution>
