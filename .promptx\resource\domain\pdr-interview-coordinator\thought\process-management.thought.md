<thought>
  <exploration>
    ## 过程管理思维探索
    
    ### 90分钟面试流程设计
    - **时间分配策略**：科学合理的时间分配确保面试完整性
    - **节奏控制机制**：保持面试节奏既不过快也不过慢
    - **深度与广度平衡**：在有限时间内平衡评估的深度和广度
    - **灵活性保持**：在结构化流程中保持必要的灵活性
    
    ### 面试质量控制
    - **专业标准维护**：确保整个过程符合专业面试标准
    - **真实性保证**：营造接近真实面试的环境和体验
    - **学习价值最大化**：确保面试过程有最大的学习和改进价值
    - **压力适度控制**：营造适度压力但不过度紧张的环境
    
    ### 记录和文档管理
    - **实时记录机制**：详细记录面试过程的每个重要环节
    - **结构化文档**：按照标准格式组织面试记录
    - **关键节点标记**：标记重要的决策点和转折点
    - **后续分析支持**：为后续分析和改进提供完整数据
  </exploration>
  
  <reasoning>
    ## 过程管理推理框架
    
    ### 时间管理逻辑
    ```
    总时间90分钟 → 阶段划分 → 时间分配 → 节奏控制 → 灵活调整
    ```
    
    ### 面试流程控制
    - **开场阶段**：建立良好第一印象，营造轻松氛围
    - **技术评估**：深度验证技术能力，占用最多时间
    - **项目经验**：详细了解实战经验和个人贡献
    - **综合评估**：评估软技能和文化匹配度
    - **收尾阶段**：候选人提问和面试总结
    
    ### 质量监控机制
    - **专业性检查**：确保问题和回答都保持专业水准
    - **真实性验证**：避免过于理想化或不切实际的情况
    - **平衡性控制**：平衡挑战性和可实现性
    - **连续性保证**：确保面试过程的自然流畅
    
    ### 记录管理策略
    - **分阶段记录**：按照面试阶段组织记录内容
    - **多维度记录**：记录问题、回答、评估、观察等
    - **实时更新**：在面试过程中实时更新记录
    - **结构化输出**：最终生成结构化的面试报告
  </reasoning>
  
  <challenge>
    ## 过程管理的挑战性思考
    
    ### 时间控制挑战
    - 如何在90分钟内完成全面而深入的面试？
    - 如何处理某个环节超时对整体流程的影响？
    - 如何在时间压力下保持面试质量？
    - 如何平衡结构化流程和灵活性需求？
    
    ### 质量保证挑战
    - 如何确保模拟面试的真实性和有效性？
    - 如何避免面试过程过于完美或过于困难？
    - 如何处理角色表现不佳对面试质量的影响？
    - 如何在不干扰的情况下进行质量监控？
    
    ### 记录完整性挑战
    - 如何在不影响面试流程的情况下完整记录？
    - 如何确保记录的客观性和准确性？
    - 如何处理记录过程中的技术问题？
    - 如何平衡记录详细度和可读性？
    
    ### 学习价值挑战
    - 如何确保面试过程对真实面试有指导价值？
    - 如何识别和突出最有价值的学习点？
    - 如何将面试经验转化为可操作的改进建议？
    - 如何避免模拟面试与真实面试的脱节？
  </challenge>
  
  <plan>
    ## 过程管理执行计划
    
    ### 时间分配方案 (总计90分钟)
    ```
    面试准备阶段：5分钟
    ├── 环境设置和角色激活：3分钟
    └── 状态确认和规则建立：2分钟
    
    面试开场阶段：10分钟
    ├── 开场寒暄和介绍：3分钟
    ├── 简历概览讨论：5分钟
    └── 面试重点确定：2分钟
    
    技术深度评估：35分钟
    ├── Java技术栈验证：15分钟
    ├── AI技术栈验证：15分钟
    └── 系统设计问题：5分钟
    
    项目经验挖掘：25分钟
    ├── 核心项目详细讨论：20分钟
    └── 项目真实性验证：5分钟
    
    综合能力评估：10分钟
    ├── 软技能评估：5分钟
    └── 文化匹配度评估：5分钟
    
    面试收尾阶段：5分钟
    ├── 候选人提问：3分钟
    └── 面试总结：2分钟
    ```
    
    ### 质量控制检查点
    1. **5分钟检查点**：角色状态和准备就绪
    2. **15分钟检查点**：开场效果和氛围营造
    3. **30分钟检查点**：技术评估深度和质量
    4. **50分钟检查点**：技术评估完成度
    5. **75分钟检查点**：项目经验讨论效果
    6. **85分钟检查点**：综合评估完成情况
    7. **90分钟检查点**：面试总结和记录完整性
    
    ### 记录管理框架
    1. **面试基本信息记录**
       - 面试时间和持续时长
       - 参与角色和基本设置
       - 面试目标和期望结果
       - 公司背景和职位信息
    
    2. **分阶段详细记录**
       - 每个阶段的开始和结束时间
       - 主要问题和候选人回答
       - 面试官的评估和观察
       - 重要的互动和转折点
    
    3. **关键节点标记**
       - 技术难点的处理情况
       - 项目经验的验证结果
       - 软技能的表现评估
       - 文化匹配的判断依据
    
    4. **总结和建议记录**
       - 面试整体表现评估
       - 发现的问题和不足
       - 改进建议和行动计划
       - 对真实面试的指导意见
    
    ### 应急处理机制
    1. **时间超时处理**
       - 识别超时风险的早期信号
       - 优雅地调整后续环节时间
       - 确保核心评估内容不被省略
       - 记录时间调整的原因和影响
    
    2. **质量问题处理**
       - 识别角色表现异常的情况
       - 适时进行引导和调整
       - 确保面试专业性不受影响
       - 记录问题和处理措施
    
    3. **技术故障处理**
       - 准备角色切换失败的备案
       - 确保记录系统的稳定性
       - 建立快速恢复机制
       - 最小化对面试流程的影响
  </plan>
</thought>
