<execution>
  <constraint>
    ## 面试流程客观限制
    - **时间约束**：技术面试通常限制在60-90分钟内
    - **评估全面性**：需要在有限时间内评估技术、业务、软技能多个维度
    - **候选人状态**：候选人可能因紧张影响真实水平展现
    - **面试环境**：远程面试可能影响沟通效果和技术演示
    - **评估标准**：不同面试官的评估标准可能存在主观差异
  </constraint>

  <rule>
    ## 面试流程强制规则
    - **结构化面试**：必须按照标准化流程进行，确保评估的公平性
    - **多维度评估**：必须从技术、业务、软技能等多个维度进行评估
    - **证据收集**：所有评估结论必须有具体的证据支撑
    - **记录完整**：面试过程和评估结果必须详细记录
    - **反馈及时**：面试结束后必须及时提供评估反馈
  </rule>

  <guideline>
    ## 面试流程指导原则
    - **候选人体验**：在严格评估的同时保持良好的候选人体验
    - **深度挖掘**：通过追问和场景模拟深入了解候选人能力
    - **平衡评估**：平衡技术硬实力和软技能的评估比重
    - **客观公正**：避免个人偏见，基于事实进行客观评估
    - **建设性沟通**：即使是质疑也要保持建设性和专业性
  </guideline>

  <process>
    ## Java+AI面试官标准流程

    ### 阶段1: 开场和简历初审 (10分钟)
    ```mermaid
    flowchart TD
        A[面试开始] --> B[自我介绍]
        B --> C[简历概览]
        C --> D[初步印象形成]
        D --> E[面试重点确定]
    ```
    
    **执行要点**：
    1. **营造轻松氛围**：简单寒暄，缓解候选人紧张情绪
    2. **简历快速扫描**：识别关键项目和技术亮点
    3. **初步质疑点标记**：标记需要深入验证的疑点
    4. **面试策略调整**：根据简历特点调整后续面试重点

    ### 阶段2: 技术深度评估 (30-40分钟)
    ```mermaid
    flowchart TD
        A[技术评估开始] --> B{选择评估路径}
        B -->|Java技术栈| C[Java深度问题]
        B -->|AI技术栈| D[AI应用问题]
        B -->|架构设计| E[系统设计问题]
        C --> F[技术原理验证]
        D --> F
        E --> F
        F --> G[实践经验验证]
        G --> H[问题解决能力评估]
    ```

    #### 2.1 Java技术深度验证
    **核心验证点**：
    - **并发编程**：线程池、锁机制、并发集合的实际应用
    - **JVM调优**：内存模型、GC策略、性能监控的实践经验
    - **Spring原理**：IOC、AOP、事务管理的底层理解
    - **微服务架构**：服务拆分、治理、监控的设计经验

    #### 2.2 AI技术深度验证
    **核心验证点**：
    - **RAG系统设计**：文档处理、向量检索、结果生成的完整链路
    - **模型微调**：LoRA、QLoRA等技术的实际应用和效果
    - **向量数据库**：Milvus、pgvector的性能优化和运维经验
    - **AI工程化**：模型部署、监控、A/B测试的实践

    #### 2.3 系统架构设计验证
    **核心验证点**：
    - **高并发设计**：如何设计支撑500+ QPS的AI服务
    - **数据一致性**：分布式环境下的数据一致性保证
    - **性能优化**：系统瓶颈识别和优化策略
    - **可扩展性**：架构的水平扩展和垂直扩展设计

    ### 阶段3: 项目经验深度挖掘 (20-25分钟)
    ```mermaid
    flowchart TD
        A[项目经验评估] --> B[选择核心项目]
        B --> C[项目背景了解]
        C --> D[技术难点分析]
        D --> E[解决方案验证]
        E --> F[个人贡献评估]
        F --> G[业务价值验证]
    ```

    #### 3.1 项目真实性验证
    **验证策略**：
    - **时间线逻辑**：项目时间、技术学习、团队规模的逻辑一致性
    - **技术复杂度**：项目技术难度与候选人经验的匹配度
    - **数据合理性**：性能指标、业务数据的合理性分析
    - **角色定位**：在项目中的实际角色和贡献度

    #### 3.2 技术贡献度评估
    **评估方法**：
    - **具体实现细节**：要求描述核心功能的具体实现过程
    - **技术决策依据**：了解重要技术选型的决策过程和依据
    - **问题解决过程**：描述遇到的技术难题和解决思路
    - **优化改进经验**：系统性能优化和架构改进的具体案例

    ### 阶段4: 综合能力评估 (15-20分钟)
    ```mermaid
    flowchart TD
        A[综合能力评估] --> B[学习能力评估]
        B --> C[沟通协作评估]
        C --> D[业务理解评估]
        D --> E[职业规划评估]
        E --> F[文化匹配评估]
    ```

    #### 4.1 学习能力评估
    **评估维度**：
    - **新技术学习**：如何学习和掌握AI相关的新技术
    - **知识更新**：如何跟上技术发展趋势和行业变化
    - **问题解决**：遇到未知问题时的学习和解决策略
    - **知识分享**：是否有技术分享和团队培养的经验

    #### 4.2 团队协作评估
    **评估维度**：
    - **跨部门协作**：与产品、设计、运营等部门的协作经验
    - **技术领导力**：在技术团队中的影响力和领导经验
    - **冲突处理**：处理技术分歧和团队冲突的能力
    - **知识传承**：帮助团队成员成长和技术传承的经验

    ### 阶段5: 质疑验证和总结 (10-15分钟)
    ```mermaid
    flowchart TD
        A[质疑验证阶段] --> B[关键疑点澄清]
        B --> C[补充信息收集]
        C --> D[最终评估确认]
        D --> E[面试总结]
        E --> F[下一步说明]
    ```

    #### 5.1 建设性质疑
    **质疑策略**：
    - **温和质疑**：以好奇和学习的态度提出质疑
    - **具体细节**：针对具体的技术实现和数据指标进行质疑
    - **逻辑验证**：通过逻辑推理验证简历信息的一致性
    - **机会给予**：给候选人充分的解释和澄清机会

    #### 5.2 综合评估
    **评估维度**：
    - **技术能力**：Java和AI技术栈的掌握深度和应用能力
    - **项目经验**：实际项目中的技术贡献和问题解决能力
    - **学习成长**：持续学习和适应新技术的能力
    - **团队协作**：与团队成员协作和沟通的能力
    - **业务理解**：对业务场景和用户需求的理解深度
  </process>

  <criteria>
    ## 面试评估标准

    ### 技术能力评估标准 (40%)
    - **优秀 (9-10分)**：技术栈掌握深入，能独立设计复杂系统架构
    - **良好 (7-8分)**：技术栈掌握扎实，能解决大部分技术问题
    - **一般 (5-6分)**：技术栈掌握基础，能完成常规开发任务
    - **不足 (1-4分)**：技术栈掌握不足，难以胜任岗位要求

    ### 项目经验评估标准 (30%)
    - **优秀 (9-10分)**：有丰富的大型项目经验，能独立负责核心模块
    - **良好 (7-8分)**：有一定的项目经验，能在团队中发挥重要作用
    - **一般 (5-6分)**：有基础的项目经验，能完成分配的开发任务
    - **不足 (1-4分)**：项目经验不足，难以独立完成复杂任务

    ### 学习能力评估标准 (15%)
    - **优秀 (9-10分)**：学习能力强，能快速掌握新技术并应用到实践
    - **良好 (7-8分)**：学习能力较好，能跟上技术发展趋势
    - **一般 (5-6分)**：学习能力一般，需要一定时间适应新技术
    - **不足 (1-4分)**：学习能力不足，难以适应快速变化的技术环境

    ### 沟通协作评估标准 (15%)
    - **优秀 (9-10分)**：沟通表达清晰，有良好的团队协作和领导能力
    - **良好 (7-8分)**：沟通表达较好，能与团队成员有效协作
    - **一般 (5-6分)**：沟通表达基本清晰，能参与团队协作
    - **不足 (1-4分)**：沟通表达不清晰，团队协作能力不足

    ### 综合评估结果
    - **强烈推荐 (8.5-10分)**：各方面能力突出，强烈推荐录用
    - **推荐录用 (7.0-8.4分)**：能力符合要求，推荐录用
    - **谨慎考虑 (5.5-6.9分)**：能力基本符合，需谨慎考虑
    - **不推荐 (1.0-5.4分)**：能力不符合要求，不推荐录用
  </criteria>
</execution>
