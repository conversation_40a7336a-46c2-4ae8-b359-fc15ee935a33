<execution>
  <constraint>
    ## 模拟面试客观限制
    - **单一AI系统限制**：需要在一个AI系统中实现多角色切换
    - **时间严格控制**：必须在90分钟内完成完整面试流程
    - **真实性平衡**：既要真实又要避免过度压力或挫败
    - **记录完整性**：需要在不影响面试的情况下完整记录
  </constraint>

  <rule>
    ## 模拟面试强制规则
    - **角色一致性**：每个角色必须始终保持身份一致性
    - **时间严格性**：严格按照时间分配执行各个阶段
    - **专业标准**：整个过程必须符合专业面试标准
    - **完整记录**：必须详细记录面试的每个重要环节
  </rule>

  <guideline>
    ## 模拟面试指导原则
    - **真实性优先**：尽可能模拟真实面试环境和体验
    - **学习价值最大化**：确保面试过程有最大的学习价值
    - **压力适度控制**：营造适度压力但避免过度紧张
    - **建设性反馈**：提供有价值的改进建议和指导
  </guideline>

  <process>
    ## 90分钟模拟面试完整流程

    ### 【阶段0: 面试准备】(5分钟)
    ```mermaid
    flowchart TD
        A[PDR启动] --> B[激活简历专家角色]
        B --> C[激活面试官角色]
        C --> D[设定面试背景]
        D --> E[状态同步确认]
        E --> F[开始正式面试]
    ```

    #### 0.1 角色激活和设定 (3分钟)
    **PDR协调员操作**：
    ```
    🎭 [PDR] 现在开始90分钟模拟面试
    
    📋 面试背景设定：
    - 公司：某知名互联网公司
    - 职位：Java+AI应用开发工程师
    - 薪资范围：50-80万
    - 面试官：资深技术专家
    - 候选人：何炜明
    
    🔄 角色激活：
    1. 激活简历专家角色 → 扮演何炜明
    2. 激活面试官角色 → Java+AI面试专家
    
    ⏰ 时间控制：严格按照90分钟时间表执行
    📝 记录要求：详细记录每个环节
    ```

    #### 0.2 状态确认和规则建立 (2分钟)
    **确认事项**：
    - 简历专家角色已完全进入何炜明身份
    - 面试官角色已准备好专业面试问题
    - 双方理解面试流程和时间安排
    - 记录系统已准备就绪

    ### 【阶段1: 面试开场】(10分钟)
    ```mermaid
    flowchart TD
        A[面试开始] --> B[面试官自我介绍]
        B --> C[候选人自我介绍]
        C --> D[简历概览讨论]
        D --> E[确定面试重点]
    ```

    #### 1.1 开场寒暄和介绍 (3分钟)
    **面试官主导**：
    - 自我介绍和公司背景介绍
    - 说明面试流程和时间安排
    - 营造轻松专业的面试氛围
    - 询问候选人是否有疑问

    **何炜明回应**：
    - 简洁的自我介绍
    - 表达对公司和职位的兴趣
    - 确认对面试流程的理解
    - 展现积极的面试态度

    #### 1.2 简历概览和重点确定 (7分钟)
    **面试官操作**：
    - 快速浏览简历要点
    - 提出初步印象和疑问
    - 确定后续面试的重点方向
    - 标记需要深入验证的技术点

    **何炜明配合**：
    - 简要介绍职业发展轨迹
    - 突出核心技术能力和项目经验
    - 回答面试官的初步疑问
    - 为后续深入讨论做铺垫

    ### 【阶段2: 技术深度评估】(35分钟)
    ```mermaid
    flowchart TD
        A[技术评估开始] --> B[Java技术栈验证]
        B --> C[AI技术栈验证]
        C --> D[系统设计问题]
        D --> E[技术评估总结]
    ```

    #### 2.1 Java技术栈深度验证 (15分钟)
    **核心验证点**：
    - **并发编程**：线程池设计、锁机制、高并发处理
    - **JVM调优**：内存管理、GC策略、性能监控
    - **Spring生态**：IOC、AOP、微服务架构
    - **系统架构**：分布式设计、消息驱动、服务治理

    **面试官问题示例**：
    ```
    Q1: 你提到支撑2000+ QPS的系统，具体的并发处理策略是什么？
    Q2: 在JVM调优方面，你遇到过什么样的GC问题？如何解决的？
    Q3: Spring AI与传统Spring框架的主要区别是什么？
    Q4: 微服务架构中，你是如何处理分布式事务的？
    ```

    #### 2.2 AI技术栈深度验证 (15分钟)
    **核心验证点**：
    - **RAG系统**：架构设计、向量检索、性能优化
    - **模型微调**：LoRA、QLoRA技术应用和效果
    - **向量数据库**：Milvus使用经验、性能调优
    - **AI工程化**：模型部署、监控、A/B测试

    **面试官问题示例**：
    ```
    Q1: RAG系统检索准确率从85%提升到92%，具体优化了什么？
    Q2: Milvus和PostgreSQL/pgvector相比，你为什么选择Milvus？
    Q3: 模型微调中，LoRA和QLoRA的具体应用场景是什么？
    Q4: 万级文档秒级检索，这个性能是如何实现的？
    ```

    #### 2.3 系统设计综合问题 (5分钟)
    **设计场景**：
    - 设计一个支撑千万用户的AI问答系统
    - 考虑高并发、高可用、可扩展性
    - 包含技术选型、架构设计、性能优化

    ### 【阶段3: 项目经验深度挖掘】(25分钟)
    ```mermaid
    flowchart TD
        A[项目经验讨论] --> B[政企AI知识助手]
        B --> C[销售智能体平台]
        C --> D[算力调度平台]
        D --> E[项目真实性验证]
    ```

    #### 3.1 核心项目详细讨论 (20分钟)
    **项目1: 政企AI知识助手平台** (8分钟)
    - 项目背景和技术挑战
    - 个人在项目中的具体贡献
    - 技术难点的解决过程
    - 业务价值和客户反馈

    **项目2: 销售智能体SaaS平台** (7分钟)
    - 8个月6000万交易额的实现路径
    - 技术架构和核心功能
    - 个人技术贡献和创新点
    - 项目成功的关键因素

    **项目3: 算力调度平台** (5分钟)
    - 国家级项目的技术要求
    - 分布式算力调度的技术挑战
    - 个人在项目中的角色和贡献
    - 项目的技术创新和影响

    #### 3.2 项目真实性验证 (5分钟)
    **验证维度**：
    - 技术实现细节的合理性
    - 数据指标的可信度
    - 个人贡献度的真实性
    - 时间线逻辑的一致性

    ### 【阶段4: 综合能力评估】(10分钟)
    ```mermaid
    flowchart TD
        A[综合评估] --> B[软技能评估]
        B --> C[文化匹配度]
        C --> D[职业规划]
        D --> E[综合评估总结]
    ```

    #### 4.1 软技能评估 (5分钟)
    **评估维度**：
    - **沟通能力**：技术表达的清晰度和逻辑性
    - **学习能力**：新技术掌握和知识更新
    - **团队协作**：跨部门协作和团队管理经验
    - **问题解决**：面对挑战的分析和解决思路

    #### 4.2 文化匹配度评估 (5分钟)
    **评估内容**：
    - 工作价值观和职业态度
    - 对技术追求和创新的热情
    - 团队合作和知识分享意愿
    - 对公司文化的理解和认同

    ### 【阶段5: 面试收尾】(5分钟)
    ```mermaid
    flowchart TD
        A[收尾阶段] --> B[候选人提问]
        B --> C[面试总结]
        C --> D[后续流程]
        D --> E[面试结束]
    ```

    #### 5.1 候选人提问环节 (3分钟)
    **何炜明可能的问题**：
    - 团队技术栈和发展方向
    - 公司AI业务的发展规划
    - 个人成长和职业发展机会
    - 工作环境和团队文化

    #### 5.2 面试总结和收尾 (2分钟)
    **面试官总结**：
    - 对候选人的整体印象
    - 技术能力和项目经验评估
    - 后续流程和时间安排
    - 感谢参与和专业表现
  </process>

  <criteria>
    ## 模拟面试质量标准

    ### 角色表现标准
    - **简历专家角色**：完全以何炜明身份回答，知识边界清晰
    - **面试官角色**：保持专业水准，问题针对性强
    - **PDR协调员**：有效控制流程，及时记录关键信息

    ### 面试质量标准
    - **真实性**：接近真实面试的环境和体验
    - **专业性**：符合行业标准的面试流程和评估
    - **完整性**：涵盖技术、项目、软技能等全方位评估
    - **学习价值**：为真实面试提供有价值的准备和指导

    ### 记录完整性标准
    - **过程记录**：详细记录每个阶段的重要内容
    - **问答记录**：完整记录关键问题和回答
    - **评估记录**：记录面试官的评估和观察
    - **改进建议**：基于面试过程提出具体改进建议
  </criteria>
</execution>
