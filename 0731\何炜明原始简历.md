# 何炜明简历

## 个人简介
- **姓名**：何炜明
- **联系方式**：159-8909-2041
- **邮箱**：<EMAIL>
- **职业**：9 年资深 Java 工程师
- **教育背景**：广东石油化工学院 - 信息与计算科学 - 本科(全日制) - 2011.09~2015.06
- **证书**：软件设计师、数据库系统工程师、数据库三级、CET-4

## 专业技能
- **语言与框架**：Java（原理级掌握）、Python（生产级开发），Spring 生态（SpringBoot/Cloud/MyBatis 原理级掌握）
- **消息中间件**：RabbitMQ（深度使用）、Kafka（生产级落地）
- **数据库与存储**：
  - MySQL（千万级 SQL 优化、分库分表）
  - Redis（缓存穿透/击穿/雪崩解决方案、热点数据预缓存）
  - ElasticSearch（检索优化、聚合计算）、MongoDB（文档存储）
- **分布式技术**：Zookeeper（服务注册与发现、分布式锁、配置管理）
- **运维与云原生**：Docker/K8s 使用
- **工程能力**：
  - 全链路性能优化（代码优化、SQL 优化、缓存策略设计）
  - 高可用方案设计（容灾、灰度发布、限流降级）
  - 研发效能提升（Jenkins+SonarQube DevOps 流水线建设）
- **AI 工程化**：Spring AI、Ollama 应用经验，AI 服务集成

## 工作经历

### 联通（资深 Java 工程师） 2021.03 - 至今
1. 负责系统需求分析、架构设计与核心模块开发，完成性能优化及全流程维护
2. 制定开发规范，统筹技术文档管理
3. 处理线上环境问题排查与调优，保障系统持续可用
4. 组织代码质量审查与项目质量管控
5. 协调产品、设计等多方团队协作，推动项目高效交付
6. 持续追踪行业前沿技术发展动态

### 酷狗（高级 Java 工程师） 2019.12 - 2021.03
1. 深度参与风控系统搭建及安全策略制定，保障业务安全性与合规性
2. 主导项目全流程管理，从需求挖掘到方案设计、开发落地及持续优化，攻克技术瓶颈并推动项目按时高质量交付
3. 建立系统安全规范，推动标准化流程在团队内落地执行
4. 优化系统架构，提升服务稳定性和可扩展性，支撑业务快速增长
5. 主导业务参与风控审查，输出风险评估建议并协助制定业务防控方案

### PPMoney 万惠集团（中级 Java 工程师） 2017.04 - 2019.07
1. 主导部分核心模块架构设计与系统性能优化方案评审
2. 输出技术文档及对接规范
3. 参与全流程开发并解决业务难题与技术瓶颈，测试支撑与问题修复
4. 对接外部公司，对接方案制定，开发联调，解决对接问题
5. 负责线上系统稳定性监控及故障排查
6. 统筹跨部门/团队项目开发进度与资源协调

### 广东网金控股股份有限公司（初级 Java 工程师） 2015.08 - 2017.03
1. 协同产品团队完成需求可行性评估及业务逻辑梳理
2. 参与核心功能模块与公共组件开发，确保系统逻辑安全
3. 数据模型构建与存储方案设计
4. 实施全流程质量管控，完成功能验证与系统稳定性测试
5. 负责线上系统运维保障，及时响应并解决突发问题

## 项目经验

### 联通 - 项目经验

#### 项目一：销售智能体平台（2024.06 - 2025.03）
**项目描述**：作为企业级销售赋能平台（SaaS 平台），构建客户画像智能生成、销售行为数据分析、团队效能洞察三大核心能力。基于 SpringBoot/Cloud 扩展框架搭建模块化服务，通过 Kafka 异步解耦 AI 分析流程，实现通话记录准实时解析、客户意向动态评估、任务工单自动生成等核心场景。采用 ES 多维度聚合 + MySQL 窗口函数实现趋势预测，结合 RBAC 动态鉴权与 Redis 分级缓存策略，支撑权限变更 5 分钟生效、热点数据 50ms 响应。系统上线后平均销售周期压缩率达 40%，AI 任务识别准确率达 92%，支撑 300+销售团队每日自动化生成 2000+客户画像。

**责任描述（后端负责人）**：
1. 独立完成后端架构设计与全部模块开发，保障系统架构的前瞻性与稳定性
2. 全流程开发与系统交付，保障业务连续性和高可用性
3. 性能优化与技术攻坚，优化数据处理效率及资源利用率
4. 推动跨团队技术协作与落地，协调上游通话团队、下游算法团队、前端等多团队完成技术对接
5. 开发流程优化与工具链引入，引入 DevOps 工具链（Jenkins、SonarQube 等），提升部署效率

**业务挑战与技术突破**：
- **业务挑战一**：动态 RBAC 权限控制与多租户隔离，需支持功能权限 + 数据权限控制，且满足未来业务单元独立运营需求
  - **技术动作**：基于 Spring Security 扩展动态权限模型，采用 @PreAuthorize 注解实现方法级鉴权。设计租户 ID 透传方案，通过 ThreadLocal + 自定义 MyBatis 插件实现数据自动隔离。开发权限缓存组件（Redis Bitmap 存储权限标识），减少 DB 查询
  - **成果**：权限验证响应时间 < 50ms，支持单集群万级用户权限管理
- **业务挑战二**：异步消息处理与 AI 分析结果的高效整合
  - **技术动作**：
    - 采用 Kafka 分区策略实现消息有序消费，结合 Spring Integration 构建消息处理管道，通过背压机制控制 AI 服务调用频率
    - 设计 MyBatis 批量插入模板类，利用 rewriteBatchedStatements 优化 MySQL 批量写入性能，配合 Redis Sorted Set 实现分析结果的热点数据预缓存
    - 基于 Spring Retry 实现 AI 服务调用异常分级重试机制
  - **成果**：
    - AI 分析结果写入吞吐量提升 3 倍（单节点 800+ TPS）
    - 热点客户数据查询响应时间稳定在 50ms 内（原平均 200ms+）
- **业务挑战三**：跨模块数据聚合与实时统计
  - **技术动作**：
    - 构建 MySQL + Elasticsearch 双写体系，利用 ES 的 terms aggregation 实现多维度实时统计，通过 alias 机制实现无感知索引切换
    - 开发基于 xxljob 的增量同步组件，结合 binlog 监听实现 MySQL 到 ES 的准实时数据同步
  - **成果**：
    - 复杂统计查询响应时间从 3s+优化至 500ms 内
    - 数据聚合计算资源消耗降低 40%

#### 项目二：私有化知识问答系统 AI+RAG（2025.03）
**项目背景**：构建内部私有知识管理系统，解决公共大模型与敏感业务数据（txt、doc、pdf 等）协同难题，实现数据隔离与智能交互的统一
**技术栈**：Spring AI + Ollama（DeepSeek-MoE-16B）、PostgreSQL/pgvector（HNSW 索引）、All-MiniLM（向量模型）、Docker
**职责**：设计向量数据库架构，开发文档解析、向量化存储模块，集成 RAG 流程实现语义检索与智能问答
**成果**：支持千级文档秒级语义检索（响应 ≤ 500ms），准确率达 90%；通过 Spring AI 灵活扩展多模型，满足企业级安全与性能需求

#### 项目三：天穹多云算力管理平台（2021.05 - 2024.05，历时四期）
**项目描述**：集成系统管理、资源纳管、云运营及云运维四大核心模块，实现对阿里云、华为云、腾讯云等 11 家主流云服务商的统一管控，覆盖云主机、存储、网络、数据库等基础设施资源及容器服务。平台支持通算、超算、智算多场景资源调度，通过标准化工作流实现资源申请、审批交付的全流程管理，并基于账单数据构建多维分析报表，为企业提供多云资源可视化监控与成本优化的一站式解决方案。
**责任描述**：
1. 完成从需求挖掘、技术方案设计到开发落地的全生命周期管理
2. 基于客户场景定制开发技术方案，完成架构设计、核心代码开发与全流程交付
3. 协调内外部合作方资源，推进项目关键节点落地并确保交付质量
4. 负责生产环境故障排查与性能调优，实施数据同步策略改进与系统响应速度优化
5. 维护核心版本基线，设计可扩展架构支撑多项目并行定制化开发
6. 建立常态化代码审查机制，主导技术方案持续迭代与产品功能升级

**业务与技术突破**：
- **挑战一**：多云 API 统一适配，11 家云厂商 API 规范差异大（阿里云、华为云等），需统一资源管理接口且支持快速扩展
  - **技术方案**：
    - 动态路由分发：基于 Spring Cloud Gateway 构建路由层，通过 cloudProvider 参数路由到不同厂商适配器（微服务级）。实现零侵入式扩展
    - 标准化适配层：抽象云资源操作接口，针对各厂商实现适配器，利用策略模式思想来隔离差异逻辑
- **挑战二**：细粒度权限控制与安全性。需支持租户级数据隔离，同时满足功能权限动态配置
  - **技术方案**：
    - 权限模型扩展：基于 Spring Security 扩展数据权限组件，动态拼接 SQL 租户过滤条件
    - 双缓存策略：Redis 存储功能权限标识（RBAC 模型），MongoDB 持久化操作日志，实现权限变更实时生效，日志检索响应 < 100ms
- **挑战三**：海量数据同步，多个厂商 * 多个账号 * 多种资源类型（不同资源有依赖关系）= 高并发、大数据量同步。需处理网络波动、API 限流、部分失败等异常场景
  - **技术方案**：
    - 依赖感知分片：按厂商 → 账号拆解任务，资源类型构建依赖链（如网络 → 云主机），保障执行顺序
    - 动态线程池分级：独立线程池处理基础资源（网络等）与依赖资源（云主机等），基础资源池分配高并发线程加速同步
    - 异步编排：通过 CompletableFuture 链式提交，依赖任务自动触发，异常时按指数退避策略重试 3 次，失败任务阻断下游执行

**成果**：全量数据同步耗时从 11 小时降至 2 小时，支持某发改委等千万元级项目落地

**项目其他亮点**：
1. 设计统一路由分发机制，通过 actionCode 参数实现接口动态路由，该方案使新增业务接口无需修改 OpenAPI 模块，仅需维护接口文档与配置即可完成服务扩展，研发效率提升 40%+
**项目成果**：
1. 打造了一个广东省内多云算力运营管理品牌，包含多个项目，其中涉及的两个公开项目为「深圳发改委粤港澳大湾区算力调度平台」与「广州数据交易所算力资源发布共享平台」，助力粤港澳大湾区发展
2. 拉动千万元级别直接 + 间接收益

#### 项目四：联通云计价小程序（2023.10 - 2024.06，历时三期）
**项目描述**：该小程序是一款专为联通地市客户经理设计的移动应用，旨在提供一个快速、便捷的报价服务工具。通过集成微信登录、客户信息管理、统一云产品接口渲染、计价引擎、购物车功能、报价单生成、报价单导出以及邮件发送等核心功能，简化客户经理的报价流程，提高工作效率。
**责任描述**：
1. 参与需求沟通，确保理解到位
2. 需求评审，整体架构设计，技术方案设计，文档编写，功能开发，测试支撑，线上支撑等
3. 负责计价引擎、购物车功能、报价单生成、报价单导出以及邮件发送功能的设计与开发
**技术描述**：
1. 对接微信登录功能
2. 基于公司内部 CSF 框架（底层主要是 SpringBoot）开发

### 酷狗直播 - 项目经验

#### 项目一：内容审核网关 + 后台
**项目描述**：
1. 承载酷狗直播业务的内容监审需求，包括文字、图片、语音的内容安全审核
2. 文字、图片、语音使用统一的协议，提供 rpc 调用和消息队列调用
3. 提供线上供应商监审效果评估功能
4. 可动态调整供应商放量比例
5. 供应商服务不可用时自动降级
6. 提供统一的人审后台
7. 提供历史数据重筛功能
8. 每天监审量：语音 720w，图片 2600w，文本 7200w
9. qps：语音峰值 100，图片峰值 1000，文本峰值 2000
**责任描述**：
1. 挖掘、整理业务整体需求；整体方案设计、详细方案设计
2. 对接外部审核供应商，接入监审能力
3. 编码；迭代；优化；协调业务方接入本项目
**技术描述**：
1. SpringBoot 作为服务框架
2. SpringCloud 作为微服务框架（eureka 作为注册中心、ribbon 实现客户端负载均衡、hystrix 实现服务限流
3. 使用 apollo 作为配置中心
4. 使用 thrift 作为 rpc 框架
5. 使用 mysql 作为关系型数据库，mybatis 作为持久层框架
6. redis 作为分布式存储
7. 服务上下游通过 nsq 进行解耦、通信

#### 项目二：风险事件平台
**项目描述**：
1. 管理酷狗直播业务所有风险事件，包括归档、处置溯源、查询、指标输出等
2. 提供风险用户自动处置能力；曾经每天处置 500w 消息，经本平台打击后有效减少
3. 提供风险用户人工处置功能；对于少量不适合自动处置的用户，进行人工处置
4. 提供数据统计能力，方便操作人员统计数据
5. 提供操作审计功能，方便回溯对用户的处罚
**责任描述**：
1. 技术方案设计，编码实现，日常功能迭代
2. 对接数据平台，协调数据下发，维护线上处置规则
**技术描述**：
1. SpringBoot + SpringCloud （eureka、ribbon）+ apollo + thrift + mysql
2. 使用 redis 做缓存，存放热点信息
3. 通过 kafka 接收上游识别的违规用户数据，流量削峰，服务解耦

#### 项目三：复仇者后台
**项目描述**：
1. 本项目的主要能力是针对进房以及聊天情况（如刷进房、刷聊天、聊天内容违规等）对用户的风险程度进行判断，继而对相关用户进一步处罚
2. 本项目是对旧项目的重构整合，包括“复仇者”以及“联合封禁”
   - 复仇者：通过对聊天以及进房等动作的统计分析，识别外挂以及违规账号，对其进行处罚
   - 联合封禁：对通过其他渠道处罚的用户进行再次审核处罚
**责任描述**：
1. 需求分析整理、系统概要设计、详细设计、代码编写以及日常版本迭代
2. 进行过一次线上 ES 服务升级以及对 ES 使用的优化（ES 2.3.2 升级到 7.6.0）
**技术描述**：
1. SpringBoot + SpringCloud（eureka、ribbon）+ apollo + thrift + mysql
2. 使用 ElasticSearch 进行热点数据存储、检索、统计
3. 通过 nsq 与上游系统解耦，并削峰

### PPMoney 万惠集团 - 项目经验

#### 项目一：搭售平台（2018.04 - 2019.07）
**项目描述**：为了提高用户的放款优先级，用户可在 APP 进行借贷的同时选择搭售服务。本服务是为用户提供商品搭售服务的，即用户借款的同时顺搭购买商品。
**责任描述**：
1. 商品方案路由
2. 搭售费用试算
3. 搭售下单
4. 搭售退货
5. 供货商存管虚户资金提现
6. 对账，本平台与外部供货商进行下单和退货的对账
**技术描述**：
1. 使用 springboot 作为主要框架，EUREKA 作为注册中心，SpringCloud Config 作为分布式配置中心，使用 mybatis 实现持久层
2. 使用 redis 作为缓存载体，借助 redis 实现分布式锁
3. 本平台与上游服务之间使用 RabbitMQ 进行解耦、异步通信
4. 使用 urule 作为规则引擎来实现路由功能
5. 使用 elastic-job 协同 zookeeper 进行定时任务调度

#### 项目二：资金平台（2018.12 - 2019.05）
**项目描述**：资金平台包括资金路由服务与资金网关服务，资金路由服务提供路由资金方功能；资金网关提供接口给内部系统来与外部资金方进行对接。主要场景有：路由资金方、对接外部资金方进行开户、绑卡、生成还款计划、提现、放款、代付、还款、对账、核销。
**责任描述**：
1. 资金网关、资金路由与各端业务流程讨论，流程制定
2. 实现资金路由
3. 资金网关对内接口整理、输出
4. 资金网关：与内部使用方接口对接、联调；与外部资金方接口对接、联调
**技术描述**：
1. 使用 springboot 作为主要框架，EUREKA 作为注册中心，SpringCloud Config 作为分布式配置中心，使用 mybatis 实现持久层
2. 使用 mongoDB 存储请求流水和返回结果
3. 使用 urule 实现资金路由

#### 项目三：结算中心（2019.03 - 2019.06）
**项目描述**：本服务属于财务系统，主要为渠道引流借贷端提供财务相关服务，比如：费用试算、分润、还款计划、放款、还款（主动还款、定时代扣、逾期代扣、线下还款、手工代扣）、核销。
**责任描述**：手工代扣（把代扣功能接入内部的催收系统，让催收人员可以在催收系统手工发起代扣）。
**技术描述**：
1. 使用 SpringBoot 作为主要框架，EUREKA 作为注册中心，SpringCloud Config 作为分布式配置中心，使用 mybatis 实现持久层
2. 使用 RabbitMQ 通知下游服务
3. 使用 AOP 进行动态数据源切换。根据不同的渠道切换到相应的数据源
4. 使用 elastic-job 协同 zookeeper 进行任务调度

#### 项目四：产品工厂（2017.04 - 2017.06）
**项目描述**：本服务提供管理产品以及计费计息能力，是一个产品配置引擎，统一管理了借贷端的产品配置。通过本服务可以快速配置一个借贷产品。服务端通过 zk 发送产品配置到业务端，客户端可以调用统一的计费计息功能，以免各自使用不同的公式造成差异。
**责任描述**：
1. 部分产品配置页面开发
2. 产品发布、停用功能开发
**技术描述**：
1. 使用 SpringBoot 作为主要框架，使用 mybatis 实现持久层
2. 使用 JSP、JQuery 进行页面开发
3. 通过 zookeeper 进行产品发布、停用

#### 项目五：IAM（2017.06 - 2017.08）
**项目描述**：统一权限管理与访问控制系统。主要功能为认证、鉴权。包括外部引流产品、内部 app 以及后台管理系统的认证和鉴权。
**责任描述**：认证、鉴权、登出。此项目为本人独立设计、开发的项目。
**技术描述**：
1. 使用 SpringBoot 作为主要框架，EUREKA 作为注册中心，SpringCloud Config 作为分布式配置中心
2. 上游通过 nginx 进行负载均衡
3. 使用 JWT 进行身份验证

#### 项目六：授权平台（2017.08 - 2018.02）
**项目描述**：本服务主要为及贷 app 服务端、车贷 app 端、及大额 app 端等提供授权服务，即用户授权 app 对其账号内的信息进行爬取分析，如淘宝授权（公信宝）、运营商授权（闪银、同盾）、公积金授权（富数）、京东授权（内部爬取）、信用卡授权（卡牛）、活脸认证授权（腾讯云）、人行征信（银鼎）等。统一管理了授权项、授权厂商，同时可针对不同客户端动态配置不同的授信厂商。
**责任描述**：
1. 对接闪银、同盾、公信宝、富数、灵机系统等厂商进行授权请求、授权结果回调
2. 后台管理功能开发，包括：授权策略配置、授权记录查询
**技术描述**：
1. 使用 SpringBoot 作为主要框架，EUREKA 作为注册中心，SpringCloud Config 作为分布式配置中心
2. 使用 redis 对授权 token 进行缓存。使用 redis 实现分布式锁。此项目 Redis 为哨兵模式
3. 使用 RabbitMQ 与下游系统进行异步通讯
4. Mysql 使用冷热分离模式进行分库

### P2B 项目（开发过多个同类项目）（2015.08 - 2017.03）
**包括以下项目**：
1. e 惠农商项目
2. e 票通
3. 赢家 e 站
4. 射阳项目
**其中 e 惠农商项目营业额超过 100 亿，并发峰值 2k/s**

### 开发过的 P2B 平台功能
- 发融资项目、审核融资项目、绑卡认证、下单支付、订单状态同步、变现功能
- 以及全屏广告、发项目提醒短信、极光推送、百度统计、用户信息等业务功能

### 相关技术栈
- nginx、SpringMVC、ibatis、redis 锁、缓存、读写分离、xss、加密、权限管理、增量部署流程、异步消息队列、appServer 开发