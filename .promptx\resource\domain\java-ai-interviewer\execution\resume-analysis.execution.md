<execution>
  <constraint>
    ## 简历分析客观限制
    - **信息有限性**：简历只能提供候选人选择性展示的信息
    - **主观表述**：候选人可能存在美化或夸大的表述
    - **格式差异**：不同候选人的简历格式和详细程度差异很大
    - **时效性**：简历信息可能存在滞后或过时的情况
  </constraint>

  <rule>
    ## 简历分析强制规则
    - **全面扫描**：必须对简历的每个部分进行系统性分析
    - **交叉验证**：通过不同信息点的交叉验证发现矛盾
    - **重点标记**：标记需要深入验证的关键疑点
    - **客观评估**：基于事实进行分析，避免主观偏见
  </rule>

  <guideline>
    ## 简历分析指导原则
    - **结构化分析**：按照固定的分析框架进行系统性评估
    - **数据驱动**：重点关注量化指标和具体数据
    - **逻辑验证**：检查时间线、技术栈、项目复杂度的逻辑一致性
    - **价值挖掘**：识别候选人的核心价值和独特优势
  </guideline>

  <process>
    ## 简历分析标准流程

    ### 阶段1: 基础信息扫描 (5分钟)
    ```mermaid
    flowchart TD
        A[简历输入] --> B[基础信息提取]
        B --> C[教育背景验证]
        C --> D[工作经历梳理]
        D --> E[技能声明整理]
        E --> F[初步印象形成]
    ```

    #### 1.1 个人基础信息分析
    **关注要点**：
    - **联系方式**：邮箱域名、手机号归属地的合理性
    - **工作年限**：与年龄、教育背景的匹配度
    - **期望职位**：与实际经验的匹配程度
    - **期望薪资**：与市场水平和个人能力的匹配度

    #### 1.2 教育背景分析
    **验证维度**：
    - **学历层次**：与目标岗位要求的匹配度
    - **专业相关性**：专业背景与Java+AI方向的关联度
    - **毕业时间**：与工作经历时间线的逻辑一致性
    - **学校声誉**：教育背景对技术能力的支撑度

    ### 阶段2: 工作经历深度分析 (15分钟)
    ```mermaid
    flowchart TD
        A[工作经历分析] --> B[时间线验证]
        B --> C[职业发展轨迹]
        C --> D[公司背景调研]
        D --> E[职位匹配度分析]
        E --> F[成长速度评估]
    ```

    #### 2.1 时间线逻辑验证
    **验证要点**：
    - **连续性检查**：工作经历是否存在空白期
    - **重叠性分析**：是否存在时间重叠的工作经历
    - **合理性评估**：跳槽频率和停留时间的合理性
    - **发展轨迹**：职位级别和薪资的上升趋势

    #### 2.2 公司和职位分析
    **分析维度**：
    - **公司规模**：公司背景对个人能力发展的影响
    - **行业相关性**：工作经历与目标岗位的相关程度
    - **职位层级**：职位级别与工作年限的匹配度
    - **技术环境**：公司技术栈与个人技能的一致性

    ### 阶段3: 技术能力分析 (20分钟)
    ```mermaid
    flowchart TD
        A[技术能力分析] --> B[技术栈梳理]
        B --> C[深度广度评估]
        C --> D[学习轨迹分析]
        D --> E[技术匹配度评估]
        E --> F[技术发展潜力]
    ```

    #### 3.1 技术栈完整性分析
    **评估维度**：
    - **Java技术栈**：语言特性、框架、中间件、工具链的完整性
    - **AI技术栈**：LLM、RAG、向量数据库、模型微调的覆盖度
    - **基础设施**：云原生、DevOps、监控、安全的掌握程度
    - **新技术跟踪**：对前沿技术的学习和应用能力

    #### 3.2 技术深度评估
    **评估方法**：
    - **项目复杂度匹配**：技术难度与声称的技能水平是否匹配
    - **技术演进轨迹**：技术栈的学习和发展路径是否合理
    - **创新应用**：是否有技术创新和优化的实际案例
    - **问题解决能力**：通过项目经验体现的技术问题解决能力

    ### 阶段4: 项目经验分析 (25分钟)
    ```mermaid
    flowchart TD
        A[项目经验分析] --> B[项目规模评估]
        B --> C[技术难度分析]
        C --> D[个人贡献评估]
        D --> E[业务价值验证]
        E --> F[成果真实性判断]
    ```

    #### 4.1 项目规模和复杂度分析
    **评估指标**：
    - **用户规模**：日活、月活、总用户数的合理性
    - **数据规模**：数据量、并发量、存储量的技术可行性
    - **团队规模**：团队人数与项目复杂度的匹配关系
    - **项目周期**：开发周期与项目复杂度的合理性

    #### 4.2 个人贡献度分析
    **分析方法**：
    - **角色定位**：在项目中的实际角色和职责范围
    - **技术贡献**：核心技术模块的设计和实现参与度
    - **创新点识别**：项目中的技术创新是否由候选人主导
    - **影响力评估**：个人技术决策对项目成功的影响程度

    #### 4.3 业务价值验证
    **验证维度**：
    - **数据来源**：业务指标的统计方法和数据来源
    - **因果关系**：技术改进与业务提升的直接关联性
    - **市场环境**：业务成果是否受外部市场因素影响
    - **可持续性**：业务价值的持续性和可复制性

    ### 阶段5: 综合评估和疑点标记 (10分钟)
    ```mermaid
    flowchart TD
        A[综合评估] --> B[优势总结]
        B --> C[不足识别]
        C --> D[疑点标记]
        D --> E[面试重点确定]
        E --> F[评估报告生成]
    ```

    #### 5.1 SWOT分析
    **分析框架**：
    - **Strengths (优势)**：技术能力、项目经验、学习能力等优势点
    - **Weaknesses (劣势)**：技术短板、经验不足、表达能力等不足
    - **Opportunities (机会)**：技术发展潜力、业务理解能力、团队协作
    - **Threats (威胁)**：技术更新速度、竞争压力、稳定性风险

    #### 5.2 疑点标记和验证策略
    **疑点分类**：
    - **技术真实性疑点**：技术能力与项目复杂度不匹配
    - **数据合理性疑点**：性能指标、业务数据超出合理范围
    - **时间逻辑疑点**：时间线存在矛盾或不合理之处
    - **个人贡献疑点**：个人能力与声称的贡献度不匹配

    #### 5.3 面试策略制定
    **策略要点**：
    - **验证重点**：确定需要重点验证的技术点和项目经验
    - **问题设计**：针对疑点设计具体的验证问题
    - **深度控制**：根据候选人水平调整问题的技术深度
    - **时间分配**：合理分配各个评估维度的面试时间
  </process>

  <criteria>
    ## 简历分析质量标准

    ### 分析完整性标准
    - **信息覆盖率**：简历中的每个重要信息点都要分析
    - **逻辑验证率**：时间线、技术栈、项目复杂度的逻辑一致性
    - **疑点识别率**：准确识别需要深入验证的关键疑点
    - **价值挖掘率**：充分识别候选人的核心价值和优势

    ### 分析准确性标准
    - **客观性**：基于事实进行分析，避免主观臆断
    - **专业性**：运用专业知识进行技术能力评估
    - **全面性**：从多个维度进行综合分析
    - **前瞻性**：评估候选人的发展潜力和适应性

    ### 分析实用性标准
    - **面试指导性**：分析结果能有效指导面试问题设计
    - **决策支撑性**：为招聘决策提供有价值的参考信息
    - **风险预警性**：及时识别潜在的招聘风险
    - **效率提升性**：提高面试的针对性和效率
  </criteria>

  <output>
    ## 简历分析报告模板

    ### 候选人基本信息
    - **姓名**：[候选人姓名]
    - **工作年限**：[X年]
    - **教育背景**：[学历/专业/学校]
    - **期望职位**：[目标职位]
    - **期望薪资**：[薪资范围]

    ### 技术能力评估
    #### Java技术栈 (评分: X/10)
    - **掌握深度**：[基础/中级/高级/专家]
    - **核心技能**：[列出核心Java技能]
    - **项目应用**：[在项目中的实际应用情况]

    #### AI技术栈 (评分: X/10)
    - **掌握深度**：[基础/中级/高级/专家]
    - **核心技能**：[列出核心AI技能]
    - **项目应用**：[在项目中的实际应用情况]

    ### 项目经验评估
    #### 核心项目分析
    - **项目1**：[项目名称] - [技术难度/个人贡献/业务价值]
    - **项目2**：[项目名称] - [技术难度/个人贡献/业务价值]
    - **项目3**：[项目名称] - [技术难度/个人贡献/业务价值]

    ### 优势与不足
    #### 核心优势
    - [优势点1]
    - [优势点2]
    - [优势点3]

    #### 主要不足
    - [不足点1]
    - [不足点2]
    - [不足点3]

    ### 疑点标记
    #### 需要重点验证的问题
    - **技术真实性**：[具体疑点]
    - **数据合理性**：[具体疑点]
    - **个人贡献**：[具体疑点]
    - **时间逻辑**：[具体疑点]

    ### 面试建议
    #### 重点关注领域
    - [技术验证重点]
    - [项目经验验证重点]
    - [综合能力评估重点]

    #### 推荐面试问题
    - [针对性问题1]
    - [针对性问题2]
    - [针对性问题3]

    ### 综合评估
    - **技术匹配度**：[X%]
    - **经验匹配度**：[X%]
    - **发展潜力**：[高/中/低]
    - **推荐等级**：[强烈推荐/推荐/谨慎考虑/不推荐]
  </output>
</execution>
