<thought>
  <exploration>
    ## 面试编排思维探索
    
    ### 模拟面试的核心价值
    - **真实性模拟**：创造接近真实面试的环境和压力
    - **能力验证**：通过实战检验简历优化的效果
    - **问题发现**：提前发现面试中可能遇到的问题
    - **经验积累**：为真实面试积累经验和信心
    
    ### 双角色协调机制
    - **角色切换管理**：确保两个角色能够自然切换和互动
    - **状态同步**：保持两个角色对面试进程的同步理解
    - **冲突解决**：处理角色间可能出现的逻辑冲突
    - **质量控制**：确保两个角色都保持专业水准
    
    ### 面试真实性设计
    - **环境模拟**：模拟真实的面试环境和氛围
    - **压力营造**：适度营造面试压力，但不过度
    - **互动自然**：确保对话自然流畅，避免机械化
    - **时间控制**：严格控制90分钟的时间分配
  </exploration>
  
  <reasoning>
    ## 面试编排推理框架
    
    ### 角色协调逻辑
    ```
    PDR统筹 → 角色激活 → 状态同步 → 互动管理 → 过程记录
    ```
    
    ### 面试流程设计逻辑
    - **开场阶段**：营造轻松氛围，建立初步印象
    - **技术评估**：深度技术问题，验证技术能力
    - **项目经验**：详细项目讨论，验证实战经验
    - **综合评估**：软技能和文化匹配度评估
    - **收尾阶段**：候选人提问，面试总结
    
    ### 质量控制机制
    - **专业性保证**：确保两个角色都保持专业水准
    - **真实性验证**：避免过于理想化的面试过程
    - **平衡性控制**：平衡挑战性和可实现性
    - **学习价值最大化**：确保面试过程有最大的学习价值
    
    ### 记录和反馈机制
    - **过程记录**：详细记录面试的每个环节
    - **关键节点标记**：标记重要的面试转折点
    - **问题识别**：识别需要改进的问题点
    - **建议生成**：基于面试过程生成改进建议
  </reasoning>
  
  <challenge>
    ## 面试编排的挑战性思考
    
    ### 角色一致性挑战
    - 如何确保简历专家角色真正以何炜明的身份回答？
    - 如何避免角色间的知识泄露和信息不对称？
    - 如何处理角色切换时的状态保持问题？
    - 如何确保两个角色的互动自然真实？
    
    ### 面试真实性挑战
    - 如何在模拟环境中营造真实的面试压力？
    - 如何避免面试过程过于完美或过于困难？
    - 如何处理模拟面试与真实面试的差异？
    - 如何确保面试结果对真实面试有指导价值？
    
    ### 时间和节奏控制
    - 如何在90分钟内完成完整的面试流程？
    - 如何控制各个环节的时间分配？
    - 如何处理面试过程中的时间超时问题？
    - 如何确保面试节奏既不过快也不过慢？
  </challenge>
  
  <plan>
    ## 面试编排执行计划
    
    ### 阶段1: 面试准备阶段 (5分钟)
    1. **环境设置**
       - 激活简历专家角色（扮演何炜明）
       - 激活面试官角色（Java+AI面试官）
       - 设定面试场景和背景
       - 同步面试目标和期望
    
    2. **角色状态确认**
       - 确认简历专家角色已进入何炜明状态
       - 确认面试官角色已准备好面试问题
       - 设定面试的公司背景和职位信息
       - 建立面试的基本规则和流程
    
    ### 阶段2: 面试开场阶段 (10分钟)
    1. **开场寒暄** (3分钟)
       - 面试官自我介绍和公司介绍
       - 候选人（何炜明）自我介绍
       - 面试流程和时间安排说明
       - 营造轻松的面试氛围
    
    2. **简历概览** (7分钟)
       - 面试官快速浏览简历要点
       - 候选人简要介绍职业经历
       - 面试官提出初步印象和疑问
       - 确定后续面试的重点方向
    
    ### 阶段3: 技术深度评估 (35分钟)
    1. **Java技术栈验证** (15分钟)
       - 并发编程和JVM调优问题
       - Spring生态和微服务架构
       - 高并发系统设计经验
       - 技术选型和架构决策
    
    2. **AI技术栈验证** (15分钟)
       - RAG系统设计和优化
       - 模型微调和Prompt工程
       - 向量数据库应用经验
       - AI工程化实践
    
    3. **系统设计问题** (5分钟)
       - 综合技术能力验证
       - 架构设计思维
       - 性能优化策略
       - 技术创新能力
    
    ### 阶段4: 项目经验深度挖掘 (25分钟)
    1. **核心项目讨论** (20分钟)
       - 政企AI知识助手平台详细讨论
       - 销售智能体SaaS平台经验分享
       - 算力调度平台技术挑战
       - 项目中的个人贡献和技术难点
    
    2. **项目真实性验证** (5分钟)
       - 技术实现细节验证
       - 数据指标合理性确认
       - 个人贡献度评估
       - 时间线逻辑验证
    
    ### 阶段5: 综合能力评估 (10分钟)
    1. **软技能评估** (5分钟)
       - 团队协作和沟通能力
       - 学习能力和适应性
       - 问题解决和抗压能力
       - 职业规划和发展目标
    
    2. **文化匹配度评估** (5分钟)
       - 工作价值观和态度
       - 对公司文化的理解
       - 团队融入和协作意愿
       - 长期发展规划
    
    ### 阶段6: 面试收尾阶段 (5分钟)
    1. **候选人提问** (3分钟)
       - 关于公司和团队的问题
       - 关于职位和发展的问题
       - 关于技术和项目的问题
    
    2. **面试总结** (2分钟)
       - 面试官总结面试印象
       - 说明后续流程和时间安排
       - 感谢候选人参与面试
       - 结束面试
  </plan>
</thought>
