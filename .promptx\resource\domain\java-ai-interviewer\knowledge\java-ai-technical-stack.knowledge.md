<knowledge>
  <domain>Java+AI技术栈知识库</domain>
  <scope>面试官需要掌握的Java和AI技术栈知识，用于评估候选人技术能力</scope>

  <content>
    ## Java技术栈核心知识

    ### Java语言特性
    ```
    基础特性：
    - 面向对象编程：封装、继承、多态
    - 泛型编程：类型安全、类型擦除
    - 反射机制：动态类加载、方法调用
    - 注解处理：编译时和运行时注解
    
    高级特性：
    - Lambda表达式和Stream API
    - 模块化系统（Java 9+）
    - 记录类型（Java 14+）
    - 模式匹配（Java 17+）
    ```

    ### JVM核心原理
    ```
    内存模型：
    - 堆内存：新生代、老年代、永久代/元空间
    - 栈内存：虚拟机栈、本地方法栈
    - 方法区：类信息、常量池、静态变量
    - 程序计数器：线程私有，记录执行位置
    
    垃圾回收：
    - 回收算法：标记-清除、复制、标记-整理
    - 收集器：Serial、Parallel、CMS、G1、ZGC
    - 调优参数：-Xms、-Xmx、-XX:NewRatio等
    - 监控工具：jstat、jmap、jhat、VisualVM
    ```

    ### Spring生态系统
    ```
    Spring Core：
    - IOC容器：Bean管理、依赖注入
    - AOP编程：切面、通知、切点
    - 事务管理：声明式事务、编程式事务
    - 数据访问：JDBC、ORM、事务同步
    
    Spring Boot：
    - 自动配置：条件注解、配置类
    - 启动器：starter依赖、自定义starter
    - 监控管理：Actuator、健康检查
    - 外部化配置：properties、yaml、环境变量
    
    Spring Cloud：
    - 服务发现：Eureka、Consul、Nacos
    - 负载均衡：Ribbon、LoadBalancer
    - 断路器：Hystrix、Resilience4j
    - 网关：Gateway、Zuul
    ```

    ### 微服务架构
    ```
    架构模式：
    - 服务拆分：按业务域、按数据、按团队
    - 通信方式：同步调用、异步消息、事件驱动
    - 数据管理：数据库分离、分布式事务
    - 服务治理：注册发现、配置管理、监控告警
    
    技术实现：
    - API网关：路由、认证、限流、监控
    - 服务网格：Istio、Linkerd、Envoy
    - 容器化：Docker、Kubernetes、Helm
    - 监控体系：Prometheus、Grafana、Jaeger
    ```

    ## AI技术栈核心知识

    ### LLM应用开发
    ```
    框架对比：
    Spring AI：
    - Java生态原生支持
    - 与Spring Boot无缝集成
    - 企业级特性：安全、监控、配置
    - 适合Java团队快速上手
    
    LangChain：
    - Python生态最成熟
    - 丰富的文档处理器
    - 强大的Prompt管理
    - 活跃的社区支持
    
    LlamaIndex：
    - 专注数据索引和检索
    - 灵活的查询引擎
    - 多种数据源支持
    - 与LangChain互补
    ```

    ### RAG系统架构
    ```
    核心组件：
    文档处理：
    - 格式支持：PDF、Word、PPT、HTML、Markdown
    - 解析工具：Apache Tika、PyPDF2、python-docx
    - 内容清洗：去除噪声、格式标准化
    - 分块策略：固定长度、语义分块、重叠分块
    
    向量化存储：
    - 嵌入模型：OpenAI、BGE、Sentence-BERT
    - 向量数据库：Milvus、Pinecone、Weaviate、pgvector
    - 索引算法：HNSW、IVF、LSH、Annoy
    - 存储优化：压缩、分片、缓存
    
    检索增强：
    - 检索策略：向量检索、关键词检索、混合检索
    - 重排序：Cross-encoder、Learning to Rank
    - 查询优化：查询扩展、查询重写
    - 结果融合：加权平均、RRF、CombSUM
    ```

    ### 向量数据库技术
    ```
    Milvus特性：
    - 分布式架构：存储计算分离、水平扩展
    - 多种索引：HNSW、IVF_FLAT、IVF_PQ、ANNOY
    - 数据类型：浮点向量、二进制向量、稀疏向量
    - 一致性保证：强一致性、最终一致性
    
    性能优化：
    - 索引参数调优：M、efConstruction、nlist
    - 查询优化：批量查询、并行查询
    - 内存管理：缓存策略、内存映射
    - 硬件优化：GPU加速、SSD存储
    
    运维管理：
    - 集群部署：Kubernetes、Docker Compose
    - 监控告警：Prometheus、Grafana
    - 备份恢复：数据备份、增量备份
    - 版本升级：滚动升级、蓝绿部署
    ```

    ### 模型微调技术
    ```
    参数高效微调：
    LoRA (Low-Rank Adaptation)：
    - 原理：低秩矩阵分解，W = W₀ + AB
    - 优势：参数量少、训练快、显存占用低
    - 适用：资源有限、快速适应特定领域
    - 参数：rank、alpha、dropout、target_modules
    
    QLoRA (Quantized LoRA)：
    - 原理：4bit量化 + LoRA微调
    - 优势：显存需求更低、消费级GPU可用
    - 技术：NF4量化、双重量化、分页优化器
    - 效果：精度损失小、微调效果好
    
    PEFT (Parameter-Efficient Fine-Tuning)：
    - 方法：LoRA、AdaLoRA、Prefix Tuning、P-Tuning
    - 框架：Hugging Face PEFT、Microsoft LoRA
    - 应用：领域适应、任务特化、多任务学习
    - 评估：BLEU、ROUGE、准确率、F1分数
    ```

    ### Prompt工程技术
    ```
    核心技术：
    Few-shot Learning：
    - 原理：通过少量示例引导模型学习
    - 设计：示例选择、格式统一、多样性平衡
    - 优化：示例质量、数量调优、顺序影响
    - 应用：分类、生成、推理、翻译
    
    Chain-of-Thought (CoT)：
    - 原理：引导模型进行步骤化推理
    - 类型：Zero-shot CoT、Few-shot CoT、Auto-CoT
    - 设计：推理链构建、中间步骤设计
    - 效果：复杂推理准确率显著提升
    
    模板优化：
    - 结构：指令、上下文、示例、查询
    - 技巧：角色设定、任务描述、输出格式
    - 迭代：A/B测试、效果评估、持续优化
    - 管理：版本控制、模板库、自动化测试
    ```

    ## 系统架构设计知识

    ### 高并发系统设计
    ```
    架构模式：
    - 负载均衡：轮询、加权、最少连接、一致性哈希
    - 缓存策略：多级缓存、缓存穿透、缓存雪崩
    - 异步处理：消息队列、事件驱动、流式处理
    - 数据库优化：读写分离、分库分表、索引优化
    
    性能优化：
    - 连接池：数据库连接池、HTTP连接池
    - 线程池：核心线程、最大线程、队列策略
    - JVM调优：堆内存、GC策略、JIT编译
    - 网络优化：Keep-Alive、HTTP/2、gRPC
    ```

    ### 分布式系统设计
    ```
    一致性模型：
    - 强一致性：线性一致性、顺序一致性
    - 弱一致性：最终一致性、因果一致性
    - 实现：2PC、3PC、Raft、Paxos
    - 权衡：CAP理论、BASE理论
    
    服务治理：
    - 服务发现：客户端发现、服务端发现
    - 负载均衡：客户端负载均衡、服务端负载均衡
    - 熔断降级：快速失败、降级策略、恢复机制
    - 限流控制：令牌桶、漏桶、滑动窗口
    ```

    ## 面试评估要点

    ### 技术深度评估
    ```
    Java技术评估：
    - 基础：语法、集合、异常、IO
    - 进阶：并发、JVM、设计模式、框架原理
    - 高级：性能调优、架构设计、源码分析
    - 专家：技术创新、团队影响、行业贡献
    
    AI技术评估：
    - 基础：机器学习概念、常用算法、工具使用
    - 进阶：深度学习、NLP、模型训练、部署
    - 高级：LLM应用、RAG系统、模型微调、工程化
    - 专家：技术创新、架构设计、业务价值创造
    ```

    ### 项目经验评估
    ```
    项目复杂度：
    - 技术栈：使用技术的广度和深度
    - 系统规模：用户量、数据量、并发量
    - 团队规模：团队人数、协作复杂度
    - 业务复杂度：业务逻辑、集成复杂度
    
    个人贡献：
    - 技术贡献：核心模块、技术难点、创新点
    - 架构贡献：系统设计、技术选型、优化改进
    - 团队贡献：技术分享、团队培养、流程改进
    - 业务贡献：需求理解、价值创造、用户体验
    ```

    ### 能力成长评估
    ```
    学习能力：
    - 技术学习：新技术掌握速度、学习方法
    - 知识更新：技术趋势跟踪、持续学习
    - 问题解决：未知问题处理、调试能力
    - 知识分享：技术输出、团队影响
    
    发展潜力：
    - 技术视野：技术趋势理解、前瞻性思考
    - 业务理解：业务场景理解、价值创造
    - 团队协作：沟通能力、领导潜力
    - 创新能力：技术创新、解决方案创新
    ```
  </content>

  <application>
    ## 知识应用指南

    ### 技术问题设计
    - 基于知识点设计分层次问题
    - 结合实际项目场景验证理解
    - 通过对比分析考察技术判断力
    - 用开放性问题评估技术视野

    ### 能力评估标准
    - 理论知识：概念理解、原理掌握
    - 实践经验：项目应用、问题解决
    - 技术判断：方案选择、权衡考虑
    - 发展潜力：学习能力、创新思维

    ### 面试技巧
    - 从简单到复杂逐步深入
    - 结合候选人经历针对性提问
    - 通过追问验证真实理解程度
    - 保持专业性和建设性
  </application>
</knowledge>
