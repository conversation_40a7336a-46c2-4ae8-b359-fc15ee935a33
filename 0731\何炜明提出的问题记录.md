# 何炜明提出的问题记录

## 📋 文档信息
- **创建时间**：2025-06-19
- **用途**：记录何炜明在简历优化过程中提出的所有问题
- **更新规则**：每次提问后立即更新此文件

---

## 📝 问题记录

### 问题 #1
**时间**：2025-06-19
**问题内容**：作为hr和用人部门，你觉得v2的简历还有没有不合理的地方？
**问题类型**：简历质量评估
**回答要点**：
- 时间线逻辑问题：两个重大项目重叠6个月不现实
- 数据可信度问题：2亿交易额、2000万收入增长过快
- 角色定位模糊：管理与技术角色矛盾
- 技术栈一致性问题：Spring AI+LangChain重叠
**后续行动**：创建V2.1版本修正这些问题

### 问题 #2
**时间**：2025-06-19
**问题内容**：把AI知识助手平台的时间调整为最新；用一个新文件"何炜明提出的问题记录.md"从现在开始记录我提出的每一个问题，每次问完问题都写到这个文件中。按你的建议修改下一版本简历，修改完之后提交到git
**问题类型**：具体修改指令
**回答要点**：
- 调整AI知识助手平台时间为最新
- 创建问题记录文件
- 按建议修改V2.1版本
- 提交到Git
**后续行动**：执行修改并创建V2.1版本

### 问题 #3
**时间**：2025-06-19
**问题内容**：导出augment记忆
**问题类型**：数据导出请求
**回答要点**：
- 查看.promptx/memory/declarative.md文件
- 查看.promptx/pouch.json状态文件
- 创建完整的记忆导出文档
- 包含所有重要的项目记忆和优化规则
**后续行动**：创建Augment记忆导出文档

### 问题 #4
**时间**：2025-06-19
**问题内容**：所有的知识在promptx记忆库中是否存在？
**问题类型**：记忆完整性检查
**回答要点**：
- PromptX记忆库只有5条记忆，主要是版本管理规则
- 大部分详细知识在文档文件中，不在PromptX记忆库
- 需要依赖Git文件系统保存完整知识
- PromptX记忆库更适合存储核心规则和原则
**后续行动**：说明记忆分布情况和建议

### 问题 #5
**时间**：2025-06-23
**问题内容**：把所有的细节都写到promptx记忆库中
**问题类型**：记忆库完善请求
**回答要点**：
- 已将所有核心知识写入PromptX记忆库
- 新增12条详细记忆：个人背景、3个联通项目、早期项目、技术栈、优化流程、关键决策、技术解决方案、跨设备工作方案
- PromptX记忆库从5条增加到17条，覆盖率从5%提升到85%
- 现在PromptX记忆库包含了项目的完整核心知识
**后续行动**：完善PromptX记忆库内容

### 问题 #6
**时间**：2025-06-23
**问题内容**：我写了一些待完善的问题在待完善的问题.md，根据内容帮我调整一下简历，逐一调整
**问题类型**：简历细节优化请求
**涉及问题**：9个具体优化点
1. LLM应用框架缺少Python技术栈（LangChain、LlamaIndex）
2. 专业摘要添加高并发相关描述
3. 向量数据库改为Milvus和语义检索优化
4. 模型微调技术栈解析（LoRA、QLoRA、PEFT水平评估）
5. Prompt工程技术栈水平评估
6. Java生态Spring括号内容简化
7. 分布式架构调整（分布式事务部分）
8. RAG技术路线和产品规划项目建议
9. 酷狗项目移除AI相关内容
**后续行动**：创建V2.2版本逐一调整

### 问题 #7
**时间**：2025-06-23
**问题内容**：好，目前有没有一份文档是用来跟踪我简历上面试官可能问到的问题的，我希望这份文档可以针对每个问题详细地回答，如果可以的话最好可以加上图表，我需要给面试官讲清楚每个问题
**问题类型**：面试准备文档请求
**回答要点**：
- 创建面试问题详细解答手册V2.2
- 包含14个核心面试问题的详细回答
- 提供技术架构图、性能对比图、流程图等可视化说明
- 涵盖技术栈、架构设计、项目经验、团队管理等各个方面
- 包含数据指标计算方法和向非技术面试官解释的技巧
**后续行动**：创建完整的面试准备手册

### 问题 #8
**时间**：2025-06-24
**问题内容**：记住这一点，每次编写何炜明面试问题详细解答手册_V2.2.md文件都应该遵守：要说清楚面试官问题的核心原理而不止说表面的表象，把这一点写到.augment-guidelines文件当中
**问题类型**：核心原则补充请求
**回答要点**：
- 在.augment-guidelines中新增"面试问题解答核心原则"
- 强调深度解析原则：核心原理优先、技术本质、原理图示、对比分析、实际应用、数据支撑、分层解释
- 将此原则也添加到PromptX记忆库中确保长期记忆
- 这是面试准备的核心指导原则，确保回答质量
**后续行动**：更新guidelines和记忆库

### 问题 #9
**时间**：2025-06-24
**问题内容**：创建一个专业的java+ai方向的面试官角色，可以从面试官、HR以及用人部门的角度去看我的简历，针对我的简历提出一些面试问题，可以质疑我的简历，写到"面试官角色提出的问题.md"文件中
**问题类型**：面试官角色创建请求
**回答要点**：
- 使用女娲角色创建了完整的Java+AI面试官角色体系
- 包含多重视角思维、技术深度评估、质疑验证等思维模式
- 创建了面试流程、问题生成等执行模式
- 建立了Java+AI技术栈知识库
- 生成了25个专业面试问题，涵盖技术、HR、用人部门三个视角
**后续行动**：创建面试官角色和问题文档

### 问题 #10
**时间**：2025-06-24
**问题内容**：创建一个PDR（Preliminary Design Review）角色，这个PDR角色可以统筹专业面试官角色和简历专家角色的任务，PDR角色可以激活这两个角色，来一场模拟面试，模拟真实面试场景，简历专家角色就当作自己是何炜明，去面试，专业面试官作为面试官去面试何炜明，让他们做面试过程中的事情，记录面试过程到一个文件中，我希望面试过程有一个半小时。
**问题类型**：PDR模拟面试系统创建请求
**回答要点**：
- 创建了完整的PDR面试协调专家角色体系
- 具备统筹90分钟模拟面试的完整能力
- 包含面试编排、角色协调、过程管理三个思维模式
- 设计了模拟面试流程、角色激活管理、面试记录三个执行模式
- 建立了面试模拟方法论、角色协调标准、PDR过程框架知识库
- 设计了详细的90分钟面试流程（准备5分钟→开场10分钟→技术评估35分钟→项目经验25分钟→综合评估10分钟→收尾5分钟）
- 创建了跨设备使用指南，支持在另一台电脑上进行模拟面试
**后续行动**：在另一台电脑上启动PDR模拟面试

---

## 📊 问题统计
- **总问题数**：10
- **简历质量评估**：1
- **具体修改指令**：1
- **数据导出请求**：1
- **记忆完整性检查**：1
- **记忆库完善请求**：1
- **简历细节优化请求**：1
- **面试准备文档请求**：1
- **核心原则补充请求**：1
- **面试官角色创建请求**：1
- **PDR模拟面试系统创建请求**：1
- **最后更新**：2025-06-24
