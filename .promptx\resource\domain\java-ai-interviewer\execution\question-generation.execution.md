<execution>
  <constraint>
    ## 问题生成客观限制
    - **简历信息有限**：只能基于简历提供的信息进行问题设计
    - **候选人差异**：不同候选人的背景和经验差异较大
    - **时间限制**：面试时间有限，问题数量需要控制
    - **技术更新快**：AI技术发展迅速，问题需要与时俱进
  </constraint>

  <rule>
    ## 问题生成强制规则
    - **针对性强**：问题必须针对候选人的具体简历内容
    - **层次分明**：问题要有基础、进阶、高级不同层次
    - **可验证性**：问题答案要能验证候选人的真实能力
    - **全面覆盖**：技术、项目、软技能等维度都要涉及
  </rule>

  <guideline>
    ## 问题生成指导原则
    - **开放性与具体性结合**：既要有开放性思考，也要有具体技术细节
    - **理论与实践并重**：既考察理论知识，也验证实践经验
    - **深度挖掘**：通过追问深入了解候选人的真实水平
    - **场景化设计**：设计贴近实际工作场景的问题
  </guideline>

  <process>
    ## 面试问题生成流程

    ### Step 1: 简历分析和问题点识别
    ```mermaid
    flowchart TD
        A[简历输入] --> B[关键信息提取]
        B --> C[技术栈分析]
        C --> D[项目经验分析]
        D --> E[疑点标记]
        E --> F[问题点清单]
    ```

    **分析维度**：
    1. **技术栈深度**：Java、AI技术的掌握程度
    2. **项目复杂度**：项目规模、技术难度、个人贡献
    3. **时间线逻辑**：工作经历、项目时间的合理性
    4. **数据真实性**：性能指标、业务数据的可信度

    ### Step 2: 分类问题设计
    ```mermaid
    graph TD
        A[问题设计] --> B[技术深度问题]
        A --> C[项目经验问题]
        A --> D[综合能力问题]
        A --> E[质疑验证问题]
        
        B --> B1[Java技术栈]
        B --> B2[AI技术栈]
        B --> B3[系统架构]
        
        C --> C1[项目背景]
        C --> C2[技术实现]
        C --> C3[问题解决]
        
        D --> D1[学习能力]
        D --> D2[团队协作]
        D --> D3[业务理解]
        
        E --> E1[技术真实性]
        E --> E2[数据合理性]
        E --> E3[贡献度验证]
    ```

    ### Step 3: 问题优先级排序
    ```mermaid
    flowchart TD
        A[问题池] --> B{重要性评估}
        B -->|核心技能| C[高优先级]
        B -->|重要经验| D[中优先级]
        B -->|补充了解| E[低优先级]
        C --> F[必问问题]
        D --> G[选择性问题]
        E --> H[时间允许问题]
    ```

    ### Step 4: 问题结构化组织
    ```mermaid
    graph LR
        A[问题组织] --> B[技术面试官视角]
        A --> C[HR视角]
        A --> D[用人部门视角]
        
        B --> B1[技术深度验证]
        B --> B2[架构设计能力]
        B --> B3[问题解决能力]
        
        C --> C1[背景真实性]
        C --> C2[稳定性评估]
        C --> C3[文化匹配度]
        
        D --> D1[业务价值创造]
        D --> D2[项目交付能力]
        D --> D3[团队协作能力]
    ```

    ## 问题模板库

    ### 技术深度验证问题模板

    #### Java技术栈问题
    ```
    基础层：
    - 你在项目中使用了Spring AI，能说说它与传统Spring框架的主要区别吗？
    - 你提到了JVM调优，具体做了哪些优化？效果如何？
    
    进阶层：
    - 你的项目支撑500+ QPS，具体的并发处理策略是什么？
    - 在微服务架构中，你是如何处理分布式事务的？
    
    高级层：
    - 如果让你重新设计这个高并发系统，你会做哪些改进？
    - 你提到的消息驱动架构，能详细说说设计思路和实现细节吗？
    ```

    #### AI技术栈问题
    ```
    基础层：
    - 你同时使用了Spring AI和LangChain，这样设计的考虑是什么？
    - RAG系统中，你是如何处理文档解析和向量化的？
    
    进阶层：
    - 你提到检索准确率从85%提升到92%，具体是怎么优化的？
    - Milvus和PostgreSQL/pgvector相比，你为什么选择Milvus？
    
    高级层：
    - 如果要支撑千万级文档的向量检索，你会如何设计架构？
    - 你对模型微调有经验，能说说LoRA和QLoRA的具体应用场景吗？
    ```

    ### 项目经验验证问题模板

    #### 项目真实性验证
    ```
    时间线验证：
    - 你在8个月内完成了6000万交易额的平台，团队规模是多少？
    - 这三个项目的时间有重叠，你是如何平衡的？
    
    技术复杂度验证：
    - 政企项目的等保三级认证，具体涉及哪些技术要求？
    - 你提到的私有化部署，数据完全不出内网是如何实现的？
    
    个人贡献验证：
    - 在5人团队中，你作为技术负责人，具体负责哪些模块？
    - 这个架构设计主要是你完成的，还是团队共同设计的？
    ```

    #### 业务价值验证
    ```
    数据来源验证：
    - 1500万合同金额是如何统计的？包含哪些项目？
    - 客户满意度95%+是通过什么方式调研的？
    
    因果关系验证：
    - 销售周期压缩30%，主要是因为哪些技术改进？
    - 效率提升2倍，具体体现在哪些环节？
    
    外部因素考虑：
    - 除了技术优化，还有哪些因素影响了业务指标？
    - 竞争对手的情况对你们的业务有什么影响？
    ```

    ### 综合能力评估问题模板

    #### 学习能力评估
    ```
    技术学习：
    - AI技术发展很快，你是如何跟上最新技术趋势的？
    - 从Java转向AI开发，你的学习路径是什么？
    
    问题解决：
    - 遇到没有现成解决方案的技术问题，你通常怎么处理？
    - 能举个例子说说你是如何学习一个全新技术的吗？
    
    知识分享：
    - 你有没有在团队内部做过技术分享？
    - 如何帮助团队成员提升AI相关技能？
    ```

    #### 团队协作评估
    ```
    跨部门协作：
    - 在AI项目中，你是如何与产品经理协作的？
    - 技术方案如何向非技术人员解释和推广？
    
    技术领导：
    - 作为技术负责人，你是如何做技术决策的？
    - 团队成员对技术方案有不同意见时，你如何处理？
    
    冲突处理：
    - 遇到技术分歧时，你通常如何解决？
    - 项目进度紧张时，如何平衡质量和速度？
    ```

    ### 质疑验证问题模板

    #### 温和质疑策略
    ```
    好奇式提问：
    - 我很好奇，8个月内实现6000万交易额，这个增长速度是如何达到的？
    - 能详细说说这个毫秒级响应是如何实现的吗？
    
    细节深入：
    - 你提到的45+政企客户，主要是哪些类型的机构？
    - 这个千万级收益具体是通过什么方式计算的？
    
    场景模拟：
    - 如果系统突然出现性能问题，你会如何快速定位和解决？
    - 假设要向CEO汇报这个AI项目的价值，你会怎么说？
    
    对比分析：
    - 为什么选择这个技术方案而不是其他更成熟的方案？
    - 与市面上的类似产品相比，你们的核心优势是什么？
    ```

    ## 问题质量控制

    ### 问题有效性检查
    - **针对性**：问题是否针对候选人的具体经历
    - **层次性**：问题是否有不同的难度层次
    - **验证性**：问题是否能有效验证候选人能力
    - **实用性**：问题是否贴近实际工作场景

    ### 问题平衡性检查
    - **技术与业务平衡**：既有技术深度，也有业务理解
    - **理论与实践平衡**：既考察理论知识，也验证实践经验
    - **个人与团队平衡**：既了解个人能力，也评估协作能力
    - **现在与未来平衡**：既评估当前水平，也考察发展潜力
  </process>

  <criteria>
    ## 问题生成质量标准

    ### 问题设计质量
    - **相关性强**：问题与候选人简历高度相关
    - **层次分明**：有基础、进阶、高级不同层次
    - **逻辑清晰**：问题表述清晰，逻辑性强
    - **可操作性**：问题具体可答，不过于抽象

    ### 验证效果评估
    - **真实性验证**：能有效验证简历信息的真实性
    - **能力评估**：能准确评估候选人的技术能力
    - **潜力判断**：能合理判断候选人的发展潜力
    - **匹配度评估**：能评估候选人与岗位的匹配度

    ### 面试体验质量
    - **专业性强**：问题体现面试官的专业水平
    - **公平公正**：问题设计公平，不带个人偏见
    - **建设性强**：即使是质疑也保持建设性
    - **候选人友好**：在严格评估的同时保持友好氛围
  </criteria>
</execution>
