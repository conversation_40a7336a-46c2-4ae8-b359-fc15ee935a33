1.llm 应用框架缺少 python 技术栈相关框架，
langchain 和 llamaindex 需要写上，再帮我根据主流技术审视一下是否还需要加其他技术栈

2.专业摘要可以添加一些高并发相关的描述，
参考这样的表述方式，不是参考内容。
•专业技术全面，长期担任大数据、数据仓库、系统架构师，能解决各种生产主机架构和运维的疑难问题。
一直到目前都是公司的最核心代码的编写者。
•真正精通数据库、数据仓库技术，对数据库调优，性能优化胜过很多优秀得dba；
真正精通数据仓库技术，在传统rdms、大数据上都有非常深入得实践；
具有丰富得管理经验。

3.向量数据库改为 milvus 和语义检索优化

4.模型微调我还不了解，不知道写三个技术栈 
LoRA、QLoRA、PEFT 多不多.请给我解析每个技术
懂这三个属于什么水平？

5.Prompt 工程列到的技术栈属于什么水平？

6.java 生态与架构里面，语言和框架，spring 生态后面的括号内容可以不要

7.分布式架构再调整一下，因为分布式事务我不是很懂怎么实现

8.rag 技术路线和产品规划扔给 augment 让它给出一个项目

9.酷狗的项目不需要 ai

